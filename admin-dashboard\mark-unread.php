<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Marking <PERSON>'s Messages as Unread ===\n";

$stmt = $db->prepare("UPDATE messages SET is_read = 0 WHERE message_id IN (104, 105) AND sender_email = '<EMAIL>'");
$result = $stmt->execute();

if ($result) {
    echo "✅ Successfully marked <PERSON>'s messages as unread\n";
} else {
    echo "❌ Failed to update messages\n";
}

// Check current unread count
$stmt = $db->query("SELECT COUNT(*) as count FROM messages WHERE is_read = 0");
$unread = $stmt->fetch();
echo "Total unread messages: {$unread['count']}\n";

// Show unread messages
$stmt = $db->prepare("
    SELECT message_id, sender_name, subject, message_category, received_at
    FROM messages 
    WHERE is_read = 0 
    ORDER BY received_at DESC
");
$stmt->execute();
$unreadMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($unreadMessages) {
    echo "\nUnread messages:\n";
    foreach ($unreadMessages as $msg) {
        echo "- ID {$msg['message_id']}: {$msg['sender_name']} - {$msg['subject']} ({$msg['message_category']}) - {$msg['received_at']}\n";
    }
} else {
    echo "No unread messages found\n";
}
?>
