# Security for upload directory
# Allow access to image files but prevent execution of PHP files

# Prevent access to PHP files
<Files "*.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent access to potentially dangerous files
<Files "*.phtml">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php3">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php4">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php5">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.pl">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.py">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.jsp">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.asp">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.sh">
    Order allow,deny
    Deny from all
</Files>

# Allow only specific image file types
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Deny everything else
<FilesMatch "^.*$">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

# Override for allowed image types (this needs to come after the deny all)
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Prevent access to .htaccess itself
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Security headers for images
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
</IfModule>
