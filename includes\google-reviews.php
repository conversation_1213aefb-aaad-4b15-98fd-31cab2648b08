<?php
/**
 * Google Business Profile Reviews Integration
 * Fetches reviews from Google Business Profile API
 */

class GoogleReviews {
    private $api_key;
    private $place_id;
    private $cache_file;
    private $cache_duration = 3600; // 1 hour cache
    
    public function __construct($api_key = null, $place_id = null) {
        // Load configuration
        $config_file = __DIR__ . '/../config/google-config.php';
        if (file_exists($config_file)) {
            require_once $config_file;
            $this->api_key = $api_key ?: (defined('GOOGLE_PLACES_API_KEY') ? GOOGLE_PLACES_API_KEY : 'YOUR_GOOGLE_API_KEY');
            $this->place_id = $place_id ?: (defined('GOOGLE_BUSINESS_PLACE_ID') ? GOOGLE_BUSINESS_PLACE_ID : 'YOUR_PLACE_ID');
            $this->cache_duration = defined('GOOGLE_REVIEWS_CACHE_DURATION') ? GOOGLE_REVIEWS_CACHE_DURATION : 3600;
        } else {
            $this->api_key = $api_key ?: 'YOUR_GOOGLE_API_KEY';
            $this->place_id = $place_id ?: 'YOUR_PLACE_ID';
            $this->cache_duration = 3600;
        }

        $this->cache_file = __DIR__ . '/../cache/google_reviews.json';

        // Create cache directory if it doesn't exist
        $cache_dir = dirname($this->cache_file);
        if (!is_dir($cache_dir)) {
            mkdir($cache_dir, 0755, true);
        }
    }
    
    /**
     * Get reviews from Google Business Profile
     * @param int $limit Number of reviews to return
     * @return array Array of reviews
     */
    public function getReviews($limit = 3) {
        // Check if manual reviews mode is enabled
        if (defined('USE_MANUAL_REVIEWS') && USE_MANUAL_REVIEWS) {
            return $this->getManualReviews($limit);
        }

        // Check if mixed mode is enabled
        if (defined('USE_MIXED_REVIEWS') && USE_MIXED_REVIEWS) {
            return $this->getMixedReviews($limit);
        }

        // Default: Use Google API
        // Check if we have cached data
        if ($this->isCacheValid()) {
            $cached_data = json_decode(file_get_contents($this->cache_file), true);
            return array_slice($cached_data, 0, $limit);
        }

        // Fetch fresh data from Google API
        $reviews = $this->fetchFromGoogle();

        if ($reviews) {
            // Cache the results
            file_put_contents($this->cache_file, json_encode($reviews));
            return array_slice($reviews, 0, $limit);
        }

        // Return fallback reviews if API fails
        return $this->getFallbackReviews($limit);
    }
    
    /**
     * Check if cached data is still valid
     * @return bool
     */
    private function isCacheValid() {
        if (!file_exists($this->cache_file)) {
            return false;
        }
        
        $cache_time = filemtime($this->cache_file);
        return (time() - $cache_time) < $this->cache_duration;
    }
    
    /**
     * Fetch reviews from Google Places API
     * @return array|false
     */
    private function fetchFromGoogle() {
        $url = "https://maps.googleapis.com/maps/api/place/details/json";
        $params = [
            'place_id' => $this->place_id,
            'fields' => 'reviews',
            'key' => $this->api_key
        ];
        
        $url .= '?' . http_build_query($params);
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Meleva Tours Website'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            return false;
        }
        
        $data = json_decode($response, true);
        
        if (!isset($data['result']['reviews'])) {
            return false;
        }
        
        return $this->formatReviews($data['result']['reviews']);
    }
    
    /**
     * Format reviews for display
     * @param array $raw_reviews
     * @return array
     */
    private function formatReviews($raw_reviews) {
        $formatted_reviews = [];
        
        foreach ($raw_reviews as $review) {
            // Skip reviews with rating less than 4 stars
            if ($review['rating'] < 4) {
                continue;
            }
            
            $formatted_reviews[] = [
                'name' => $this->formatName($review['author_name']),
                'rating' => $review['rating'],
                'text' => $this->truncateText($review['text'], 150),
                'date' => $this->formatDate($review['time']),
                'avatar' => $review['profile_photo_url'] ?? null,
                'source' => 'Google'
            ];
        }
        
        // Sort by rating (highest first)
        usort($formatted_reviews, function($a, $b) {
            return $b['rating'] - $a['rating'];
        });
        
        return $formatted_reviews;
    }
    
    /**
     * Format reviewer name for privacy
     * @param string $name
     * @return string
     */
    private function formatName($name) {
        $parts = explode(' ', $name);
        if (count($parts) > 1) {
            return $parts[0] . ' ' . substr($parts[1], 0, 1) . '.';
        }
        return $name;
    }
    
    /**
     * Truncate review text
     * @param string $text
     * @param int $length
     * @return string
     */
    private function truncateText($text, $length = 150) {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . '...';
    }
    
    /**
     * Format timestamp to readable date
     * @param int $timestamp
     * @return string
     */
    private function formatDate($timestamp) {
        return date('F Y', $timestamp);
    }
    
    /**
     * Get manually curated reviews
     * @param int $limit
     * @return array
     */
    private function getManualReviews($limit = 3) {
        $config_file = __DIR__ . '/../config/google-config.php';
        if (file_exists($config_file)) {
            // Clear any existing manual_reviews variable
            unset($manual_reviews);

            // Clear opcode cache if available to ensure fresh file content
            if (function_exists('opcache_invalidate')) {
                opcache_invalidate($config_file, true);
            }

            // Use include instead of require_once to ensure fresh data is loaded
            include $config_file;

            if (isset($manual_reviews) && is_array($manual_reviews)) {
                error_log("getManualReviews: Found " . count($manual_reviews) . " manual reviews");
                error_log("getManualReviews: First review name: " . ($manual_reviews[0]['name'] ?? 'N/A'));

                // Clean up HTML entities in the reviews
                $cleaned_reviews = [];
                foreach ($manual_reviews as $review) {
                    if (!empty($review['name'])) { // Only include non-empty reviews
                        $cleaned_review = $review;
                        $cleaned_review['name'] = html_entity_decode($review['name'], ENT_QUOTES, 'UTF-8');
                        $cleaned_review['text'] = html_entity_decode($review['text'], ENT_QUOTES, 'UTF-8');
                        $cleaned_review['date'] = html_entity_decode($review['date'], ENT_QUOTES, 'UTF-8');
                        $cleaned_review['location'] = html_entity_decode($review['location'] ?? '', ENT_QUOTES, 'UTF-8');
                        $cleaned_reviews[] = $cleaned_review;
                    }
                }

                return array_slice($cleaned_reviews, 0, $limit);
            } else {
                error_log("getManualReviews: manual_reviews variable not set or not array in config file");
            }
        } else {
            error_log("getManualReviews: Config file not found: " . $config_file);
        }

        // Fallback to default reviews if config not found
        error_log("getManualReviews: Using fallback reviews");
        return $this->getFallbackReviews($limit);
    }

    /**
     * Get mixed reviews (manual + Google)
     * @param int $limit
     * @return array
     */
    private function getMixedReviews($limit = 3) {
        $manual_count = ceil($limit / 2); // Half manual
        $google_count = $limit - $manual_count; // Half Google

        $manual_reviews = $this->getManualReviews($manual_count);

        // Get Google reviews
        $google_reviews = [];
        if ($this->isCacheValid()) {
            $cached_data = json_decode(file_get_contents($this->cache_file), true);
            $google_reviews = array_slice($cached_data, 0, $google_count);
        } else {
            $fetched_reviews = $this->fetchFromGoogle();
            if ($fetched_reviews) {
                file_put_contents($this->cache_file, json_encode($fetched_reviews));
                $google_reviews = array_slice($fetched_reviews, 0, $google_count);
            }
        }

        // Combine and shuffle
        $mixed_reviews = array_merge($manual_reviews, $google_reviews);
        shuffle($mixed_reviews);

        return array_slice($mixed_reviews, 0, $limit);
    }

    /**
     * Get fallback reviews when API is unavailable
     * @param int $limit
     * @return array
     */
    private function getFallbackReviews($limit = 3) {
        $fallback_reviews = [
            [
                'name' => 'Eva K.',
                'rating' => 5,
                'text' => 'The safari experience was absolutely amazing! The guides were incredibly knowledgeable and made every moment unforgettable. Meleva Tours exceeded all our expectations.',
                'date' => 'June 2024',
                'avatar' => null,
                'source' => 'Google'
            ],
            [
                'name' => 'Sarah M.',
                'rating' => 5,
                'text' => 'Diani Beach was absolute paradise, and the planning was seamless from start to finish. I highly recommend Meleva Tours for anyone seeking authentic African experiences!',
                'date' => 'May 2024',
                'avatar' => null,
                'source' => 'Google'
            ],
            [
                'name' => 'James L.',
                'rating' => 5,
                'text' => 'Mount Kenya trek was challenging but incredibly rewarding. The team provided excellent support throughout the journey. An adventure I\'ll never forget!',
                'date' => 'April 2024',
                'avatar' => null,
                'source' => 'Google'
            ],
            [
                'name' => 'Michael R.',
                'rating' => 5,
                'text' => 'Professional service from start to finish. The Maasai Mara experience was breathtaking and the accommodation was top-notch.',
                'date' => 'March 2024',
                'avatar' => null,
                'source' => 'Google'
            ],
            [
                'name' => 'Lisa T.',
                'rating' => 5,
                'text' => 'Exceptional customer service and attention to detail. Our family safari was perfectly organized and created memories that will last a lifetime.',
                'date' => 'February 2024',
                'avatar' => null,
                'source' => 'Google'
            ]
        ];
        
        return array_slice($fallback_reviews, 0, $limit);
    }
    
    /**
     * Generate star rating HTML
     * @param int $rating
     * @return string
     */
    public function generateStarRating($rating) {
        $html = '<div class="flex text-orange-500">';
        
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $html .= '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>';
            } else {
                $html .= '<svg class="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>';
            }
        }
        
        $html .= '</div>';
        return $html;
    }
    
    /**
     * Generate avatar HTML
     * @param array $review
     * @return string
     */
    public function generateAvatar($review) {
        if ($review['avatar']) {
            return '<img src="' . htmlspecialchars($review['avatar']) . '" alt="' . htmlspecialchars($review['name']) . '" class="w-12 h-12 rounded-full object-cover mr-4">';
        } else {
            $initial = strtoupper(substr($review['name'], 0, 1));
            return '<div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">' . $initial . '</div>';
        }
    }
}
