<?php
/**
 * FAQ Helper for Meleva Tours and Travel
 * Provides FAQ sections with structured data for better SEO
 */

class FAQ {
    private $faqs = [];
    
    public function __construct() {
        $this->loadDefaultFAQs();
    }
    
    private function loadDefaultFAQs() {
        $this->faqs = [
            [
                'question' => 'What is included in your safari packages?',
                'answer' => 'Our safari packages typically include accommodation, meals, professional guide services, park entrance fees, game drives, and transportation. Specific inclusions vary by package, so please check individual tour details or contact us for comprehensive information.'
            ],
            [
                'question' => 'What is the best time to visit Kenya for a safari?',
                'answer' => 'Kenya offers excellent safari experiences year-round. The dry seasons (June-October and December-March) are ideal for wildlife viewing as animals gather around water sources. The Great Migration in Maasai Mara typically occurs from July to October.'
            ],
            [
                'question' => 'Do I need a visa to visit Kenya?',
                'answer' => 'Most visitors need a visa to enter Kenya. You can apply for an eVisa online before travel or obtain a visa on arrival at the airport. Requirements vary by nationality, so please check with the Kenyan embassy or consulate in your country.'
            ],
            [
                'question' => 'What should I pack for a safari?',
                'answer' => 'Essential items include comfortable clothing in neutral colors, a wide-brimmed hat, sunscreen, insect repellent, binoculars, camera with extra batteries, and sturdy walking shoes. We provide a detailed packing list upon booking confirmation.'
            ],
            [
                'question' => 'Are your tours suitable for families with children?',
                'answer' => 'Yes! We offer family-friendly safari options suitable for children of all ages. Our experienced guides are skilled at engaging young travelers and ensuring everyone has a memorable and educational experience.'
            ],
            [
                'question' => 'What safety measures do you have in place?',
                'answer' => 'Safety is our top priority. All our guides are professionally trained, vehicles are regularly maintained and equipped with safety equipment, and we maintain comprehensive insurance coverage. We also provide safety briefings before all activities.'
            ],
            [
                'question' => 'Can you accommodate dietary restrictions?',
                'answer' => 'Absolutely! We can accommodate various dietary requirements including vegetarian, vegan, gluten-free, and other special dietary needs. Please inform us of any restrictions when booking so we can make appropriate arrangements.'
            ],
            [
                'question' => 'How far in advance should I book my safari?',
                'answer' => 'We recommend booking at least 3-6 months in advance, especially for peak seasons (July-October and December-January). This ensures better availability and rates for accommodations and allows time for visa processing and travel preparations.'
            ],
            [
                'question' => 'What payment methods do you accept?',
                'answer' => 'We accept various payment methods including credit cards, bank transfers, and online payments through our secure payment system. A deposit is typically required to confirm your booking, with the balance due before departure.'
            ],
            [
                'question' => 'Do you offer travel insurance?',
                'answer' => 'While we don\'t directly provide travel insurance, we strongly recommend purchasing comprehensive travel insurance that covers medical emergencies, trip cancellation, and evacuation. We can recommend trusted insurance providers.'
            ]
        ];
    }
    
    public function addFAQ($question, $answer) {
        $this->faqs[] = [
            'question' => $question,
            'answer' => $answer
        ];
    }
    
    public function generateFAQSection($title = 'Frequently Asked Questions', $limit = null) {
        $displayFAQs = $limit ? array_slice($this->faqs, 0, $limit) : $this->faqs;
        
        if (empty($displayFAQs)) {
            return '';
        }
        
        $html = '<section class="mt-16 bg-white rounded-2xl shadow-lg p-8">';
        $html .= '<div class="text-center mb-8">';
        $html .= '<h2 class="text-2xl md:text-3xl font-semibold text-gray-900 mb-4">' . htmlspecialchars($title) . '</h2>';
        $html .= '<div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto"></div>';
        $html .= '</div>';
        
        $html .= '<div class="space-y-4">';
        
        foreach ($displayFAQs as $index => $faq) {
            $html .= '<div class="faq-item border border-gray-200 rounded-lg">';
            $html .= '<button class="faq-question w-full text-left p-4 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50" ';
            $html .= 'onclick="toggleFAQ(' . $index . ')" aria-expanded="false" aria-controls="faq-answer-' . $index . '">';
            $html .= '<div class="flex items-center justify-between">';
            $html .= '<h3 class="text-sm md:text-lg font-medium text-gray-900 pr-4">' . htmlspecialchars($faq['question']) . '</h3>';
            $html .= '<svg class="faq-icon w-5 h-5 text-orange-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
            $html .= '</svg>';
            $html .= '</div>';
            $html .= '</button>';
            $html .= '<div id="faq-answer-' . $index . '" class="faq-answer hidden px-4 pb-4">';
            $html .= '<p class="text-gray-600 leading-relaxed">' . htmlspecialchars($faq['answer']) . '</p>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        // Add contact CTA
        $html .= '<div class="text-center mt-8 pt-8 border-t border-gray-200">';
        $html .= '<p class="text-gray-600 mb-4">Have more questions? We\'re here to help!</p>';
        $html .= '<a href="contact.php" class="inline-flex items-center bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">';
        $html .= '<i class="fas fa-phone mr-2"></i> Contact Us';
        $html .= '</a>';
        $html .= '</div>';
        
        $html .= '</section>';
        
        return $html;
    }
    
    public function generateFAQScript() {
        return '
        <script>
        function toggleFAQ(index) {
            const answer = document.getElementById("faq-answer-" + index);
            const question = answer.previousElementSibling;
            const icon = question.querySelector(".faq-icon");
            
            if (answer.classList.contains("hidden")) {
                // Close all other FAQs
                document.querySelectorAll(".faq-answer").forEach(el => {
                    if (el !== answer) {
                        el.classList.add("hidden");
                        el.previousElementSibling.setAttribute("aria-expanded", "false");
                        el.previousElementSibling.querySelector(".faq-icon").style.transform = "rotate(0deg)";
                    }
                });
                
                // Open this FAQ
                answer.classList.remove("hidden");
                question.setAttribute("aria-expanded", "true");
                icon.style.transform = "rotate(180deg)";
            } else {
                // Close this FAQ
                answer.classList.add("hidden");
                question.setAttribute("aria-expanded", "false");
                icon.style.transform = "rotate(0deg)";
            }
        }
        </script>';
    }
    
    public function generateStructuredData() {
        if (empty($this->faqs)) {
            return '';
        }
        
        $faqItems = [];
        foreach ($this->faqs as $faq) {
            $faqItems[] = [
                "@type" => "Question",
                "name" => $faq['question'],
                "acceptedAnswer" => [
                    "@type" => "Answer",
                    "text" => $faq['answer']
                ]
            ];
        }
        
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "FAQPage",
            "mainEntity" => $faqItems
        ];
        
        return '<script type="application/ld+json">' . json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . '</script>';
    }
    
    public function getPageSpecificFAQs($pageType) {
        switch ($pageType) {
            case 'destination':
                return [
                    [
                        'question' => 'What wildlife can I expect to see at this destination?',
                        'answer' => 'Wildlife varies by destination and season. Our experienced guides will help you spot the diverse animals that call this area home, from the Big Five to numerous bird species and smaller mammals.'
                    ],
                    [
                        'question' => 'What accommodation options are available?',
                        'answer' => 'We offer a range of accommodation options from luxury lodges to comfortable tented camps, all selected for their quality, location, and authentic safari experience.'
                    ],
                    [
                        'question' => 'How long should I stay at this destination?',
                        'answer' => 'We recommend at least 2-3 days to fully experience the destination, though longer stays allow for more wildlife sightings and a deeper connection with the environment.'
                    ]
                ];
                
            case 'package':
                return [
                    [
                        'question' => 'What is included in this specific package?',
                        'answer' => 'This package includes accommodation, meals as specified, professional guide services, transportation, park fees, and activities mentioned in the itinerary. Please check the detailed inclusions list.'
                    ],
                    [
                        'question' => 'Can this package be customized?',
                        'answer' => 'Yes! We can customize most packages to suit your preferences, budget, and schedule. Contact us to discuss modifications and create your perfect safari experience.'
                    ],
                    [
                        'question' => 'What is the group size for this tour?',
                        'answer' => 'Group sizes vary by package. We offer both small group tours (4-8 people) and private tours for a more personalized experience. Check the package details or contact us for specific information.'
                    ]
                ];
                
            default:
                return array_slice($this->faqs, 0, 5);
        }
    }
}
