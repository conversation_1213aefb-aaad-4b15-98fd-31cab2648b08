# Google Business Profile Reviews Integration Setup

This guide will help you set up the Google Business Profile reviews integration for your Meleva Tours website.

## What's Been Implemented

✅ **Google Reviews PHP Class** - Fetches reviews from Google Places API
✅ **Manual Review Selection** - Choose exactly which reviews to display
✅ **Admin Interface** - Easy-to-use review management panel
✅ **Multiple Display Modes** - Manual, Google API, or Mixed reviews
✅ **Caching System** - Stores reviews for 1 hour to avoid API rate limits
✅ **Fallback System** - Shows default reviews if API is unavailable
✅ **Dynamic Testimonials** - Homepage displays your chosen reviews
✅ **Professional Design** - Google branding and "View All Reviews" button

## Setup Steps

### Step 1: Get Google Places API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **Places API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Places API"
   - Click "Enable"
4. Create an API Key:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the API key

### Step 2: Secure Your API Key (Important!)

1. In Google Cloud Console, click on your API key
2. Under "API restrictions", select "Restrict key"
3. Choose "Places API" only
4. Under "Application restrictions", you can:
   - Select "HTTP referrers" and add your domain: `*.melevatours.co.ke/*`
   - Or select "IP addresses" and add your server IP

### Step 3: Find Your Google Business Place ID

1. Go to [Place ID Finder](https://developers.google.com/maps/documentation/places/web-service/place-id)
2. Search for "Meleva Tours and Travel" or your business address
3. Copy the Place ID (starts with something like "ChIJ...")

### Step 4: Update Configuration

Edit the file `config/google-config.php` and replace:

```php
define('GOOGLE_PLACES_API_KEY', 'YOUR_ACTUAL_API_KEY_HERE');
define('GOOGLE_BUSINESS_PLACE_ID', 'YOUR_ACTUAL_PLACE_ID_HERE');
```

### Step 5: Choose Your Review Display Mode

You have 3 options for displaying reviews:

**Option 1: Manual Reviews (Recommended)**
- Go to `admin-dashboard/manage-reviews.php` in your admin panel
- Select "Manual Reviews Only"
- Add, edit, or delete manual reviews with the intuitive interface
- Configure reviewer names, review content, and ratings
- Real-time preview of changes on the homepage
- Perfect for showcasing your best customer feedback

**Option 2: Google API Reviews**
- Automatically fetches real reviews from Google Business Profile
- Requires API setup (Steps 1-4 above)
- Shows fresh, authentic customer reviews

**Option 3: Mixed Reviews**
- Combines manual reviews with Google API reviews
- Best of both worlds - control + authenticity

### Step 6: Test the Integration

1. Visit your homepage
2. Scroll to the "What Our Travelers Say" section
3. You should see your chosen reviews with Google branding

## Configuration Options

In `config/google-config.php`, you can adjust:

- `GOOGLE_REVIEWS_CACHE_DURATION` - How long to cache reviews (default: 1 hour)
- `MAX_REVIEWS_DISPLAY` - Number of reviews to show (default: 3)
- `MIN_REVIEW_RATING` - Minimum star rating to display (default: 4 stars)
- `MAX_REVIEW_TEXT_LENGTH` - Maximum review text length (default: 150 characters)

## Features

### Automatic Caching
- Reviews are cached for 1 hour to avoid API rate limits
- Cache files are stored in `cache/google_reviews.json`

### Fallback System
- If the Google API is unavailable, shows curated fallback reviews
- Ensures your testimonials section always displays content

### Privacy Protection
- Customer names are automatically formatted (e.g., "John Smith" becomes "John S.")
- Only shows reviews with 4+ stars for better brand image

### Professional Design
- Google branding and icons
- Star ratings display
- "View All Reviews" button links to your Google Business Profile
- Responsive design works on all devices

## Troubleshooting

### Reviews Not Loading?
1. Check your API key is correct in `config/google-config.php`
2. Verify the Places API is enabled in Google Cloud Console
3. Check the Place ID is correct
4. Look for error messages in your server logs

### API Quota Exceeded?
- The system caches reviews for 1 hour to minimize API calls
- Google Places API has generous free quotas
- Consider increasing cache duration if needed

### Want to Customize?
- Use the admin interface at `admin-dashboard/manage-reviews.php` for easy management
- Edit `includes/google-reviews.php` to modify functionality
- Update fallback reviews in `config/google-config.php`
- Modify the display in `index.php` testimonials section

### Admin Interface Features
- **Review Management**: Add, edit, delete manual reviews
- **Display Mode Control**: Switch between manual, Google API, or mixed modes
- **Real-time Updates**: Changes appear immediately on the homepage
- **Form Validation**: Ensures all review data is properly formatted
- **Cache Management**: Automatic cache invalidation when reviews are updated

## Cost Information

- Google Places API: Free tier includes 100,000 requests per month
- With 1-hour caching, you'll use approximately 720 requests per month
- Well within the free tier limits

## Support

If you need help setting this up:
1. Check that all files are in the correct locations
2. Verify your Google Cloud Console settings
3. Test with a simple API call to confirm your credentials work

The system will gracefully fall back to static reviews if there are any issues with the Google API integration.
