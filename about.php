<?php
// Set performance headers first, before any output
require_once 'includes/performance.php';
Performance::setPerformanceHeaders();

// Set SEO data for about page
$seoTitle = 'About Us - Your Trusted Safari Partner';
$seoDescription = 'Learn about Meleva Tours & Travel, Kenya\'s premier safari company. Discover our story, values, and commitment to providing authentic African safari experiences since our founding.';
$seoKeywords = 'about Meleva Tours, Kenya safari company, African tour operator, safari experts, travel company Kenya, authentic safari experiences';
$seoImage = 'images/about-hero.jpg';
$seoType = 'website';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        /* ===================================
           ABOUT PAGE SPECIFIC STYLES
           =================================== */

        /* Feature cards specific styling */
        .feature-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-left-color: #f97316;
        }

        /* Values section styling */
        .value-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f97316, #ea580c);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        .value-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 24px rgba(249, 115, 22, 0.3);
        }

        /* Story section image styling */
        .story-image-container {
            position: relative;
        }

        .story-image-container::before,
        .story-image-container::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            opacity: 0.2;
        }

        .story-image-container::before {
            width: 128px;
            height: 128px;
            background: #f97316;
            bottom: -24px;
            right: -24px;
        }

        .story-image-container::after {
            width: 96px;
            height: 96px;
            background: #dc2626;
            top: -24px;
            left: -24px;
        }

        /* Hero Section Styles */
        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(249, 115, 22, 0.3));
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .parallax-bg {
            background-attachment: fixed;
        }

        @media (max-width: 768px) {
            .parallax-bg {
                background-attachment: scroll;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="relative bg-cover bg-center bg-no-repeat parallax-bg py-20 px-4" style="background-image: url('images/nav-bg.jpg')">
        <div class="absolute inset-0 hero-overlay"></div>

        <!-- Hero Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center text-white">
            <h1 class="text-3xl md:text-5xl font-bold mb-6 text-shadow">
                About <span class="text-orange-400">Meleva Tours & Travel</span>
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8"></div>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto text-shadow">
                Discover the heart and soul behind Kenya's premier safari experience.
            </p>
        </div>
    </section>
    
    <!-- Our Story Section -->
    <section id="our-story" class="py-20 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="order-2 lg:order-1">
                    <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                        Our <span class="gradient-text">Story</span>
                    </h2>
                    <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mb-8"></div>
                    <p class="text-lg md:text-xl text-gray-700 mb-6 leading-relaxed">
                        Meleva Tours was born from a profound passion for the untamed beauty and rich cultural tapestry of Africa. Nestled in the heart of Mavueni - Kilifi County, Kenya, our roots run deep in the very landscapes we invite you to explore.
                    </p>
                    <p class="text-lg md:text-xl text-gray-700 mb-6 leading-relaxed">
                        For years, we have dedicated ourselves to crafting unparalleled safari and travel experiences that go beyond mere sightseeing. We believe that true exploration is about connection – connecting with nature, with diverse cultures, and with the spirit of adventure that Africa so readily ignites.
                    </p>
                    <p class="text-lg md:text-xl text-gray-700 leading-relaxed">
                        Our journey began with a simple yet powerful vision: to share the magic of this continent with the world, fostering a deeper appreciation for its wildlife, its people, and its breathtaking vistas.
                    </p>
                </div>
                <div class="order-1 lg:order-2">
                    <div class="relative">
                        <img src="images/maasai-mara.jpg" alt="African Safari Landscape" class="rounded-2xl shadow-2xl w-full h-96 object-cover">
                        <div class="absolute -bottom-6 -right-6 w-32 h-32 bg-orange-500 rounded-full opacity-20"></div>
                        <div class="absolute -top-6 -left-6 w-24 h-24 bg-red-500 rounded-full opacity-20"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
        
    <!-- Why Choose Us Section -->
    <section class="py-20 px-4 bg-gray-900 text-white parallax-bg" style="background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('images/ban.jpg')">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-white mb-6">
                    Why Choose <span class="gradient-text">Meleva Tours and Travel</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-white max-w-3xl mx-auto">
                    Choosing Meleva Tours means choosing an authentic African adventure guided by expertise and driven by passion.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover border-l-4 safari-border">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Local Expertise</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Our intimate knowledge of Mavueni, Kilifi County, and the broader region allows us to offer unique insights and access to hidden gems that larger operators might overlook.
                    </p>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover border-l-4 safari-border">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Personalized Service</h3>
                    <p class="text-gray-600 leading-relaxed">
                        We pride ourselves on our personalized approach, understanding that every traveler is unique. Every detail is tailored to your preferences for a truly bespoke experience.
                    </p>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover border-l-4 safari-border">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Sustainable Tourism</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Our commitment to sustainable tourism means your journey directly contributes to local communities and conservation of Africa's precious ecosystems.
                    </p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Values Section -->
    <section class="py-20 px-4 bg-white">
        <div class="max-w-7xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                Our <span class="gradient-text">Values</span>
            </h2>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-16"></div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Value 1 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Authenticity</h3>
                    <p class="text-gray-600">Genuine experiences that showcase the real Africa, not tourist facades</p>
                </div>

                <!-- Value 2 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Conservation</h3>
                    <p class="text-gray-600">Protecting wildlife and ecosystems for future generations</p>
                </div>

                <!-- Value 3 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Community</h3>
                    <p class="text-gray-600">Supporting local communities and creating lasting partnerships</p>
                </div>

                <!-- Value 4 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Excellence</h3>
                    <p class="text-gray-600">Delivering exceptional service and unforgettable experiences</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Call to Action Section -->
    <section class="relative py-20 px-4 text-white overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image: url('images/cta-banner.jpg'); filter: blur(2px);">
            <!-- Dark Overlay for better text readability -->
            <div class="absolute inset-0 bg-black bg-opacity-80"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold mb-6 text-white drop-shadow-lg">
                Ready to Explore Africa?
            </h2>
            <p class="text-lg md:text-xl mb-10 text-white drop-shadow-md max-w-3xl mx-auto leading-relaxed">
                Let us craft your perfect African adventure. From the coastal beauty of Kilifi to the wild heart of Kenya's national parks, your journey of a lifetime awaits.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="request-quote.php" class="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                    Get Your Quote
                </a>
                <a href="tours.php" class="border-2 border-white text-white hover:bg-white hover:text-orange-500 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl">
                    View Our Tours
                </a>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 border-2 border-white opacity-20 rounded-full"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 border-2 border-orange-400 opacity-30 rounded-full"></div>
        <div class="absolute top-1/2 left-5 w-3 h-3 bg-orange-400 opacity-40 rounded-full"></div>
        <div class="absolute top-1/4 right-20 w-2 h-2 bg-white opacity-50 rounded-full"></div>
    </section>
    
    <!-- Scripts --></script>
    <script>
        // ===================================
        // ABOUT PAGE SPECIFIC JAVASCRIPT
        // ===================================

        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            // Observe all sections for fade-in animation
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });

            // Add hover effects to value icons
            const valueIcons = document.querySelectorAll('.value-icon');
            valueIcons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });

                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>

    <?php include 'footer.php'; ?>

</body>
</html>

