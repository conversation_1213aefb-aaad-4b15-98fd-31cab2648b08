<?php
// Start output buffering to prevent header issues
ob_start();

// Define admin access constant
define('ADMIN_ACCESS', true);

require_once '../config/config.php';
require_once '../classes/models.php';

// Simple authentication check without CSRF for AJAX
Auth::startSession();
Auth::requireLogin();

// Check admin role
$user = Auth::getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Insufficient permissions']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['message_ids']) || !is_array($input['message_ids']) || empty($input['message_ids'])) {
    echo json_encode(['success' => false, 'error' => 'No messages selected']);
    exit;
}

if (!isset($input['action']) || !in_array($input['action'], ['mark_read', 'mark_unread', 'delete'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid action']);
    exit;
}

$messageIds = array_map('intval', $input['message_ids']);
$action = $input['action'];
$messageModel = new Message();

try {
    $successCount = 0;
    
    foreach ($messageIds as $messageId) {
        switch ($action) {
            case 'mark_read':
                if ($messageModel->markAsRead($messageId)) {
                    $successCount++;
                }
                break;
                
            case 'mark_unread':
                $sql = "UPDATE messages SET is_read = 0 WHERE message_id = :id";
                $stmt = $messageModel->getDb()->prepare($sql);
                $stmt->bindParam(':id', $messageId);
                if ($stmt->execute()) {
                    $successCount++;
                }
                break;
                
            case 'delete':
                $sql = "DELETE FROM messages WHERE message_id = :id";
                $stmt = $messageModel->getDb()->prepare($sql);
                $stmt->bindParam(':id', $messageId);
                if ($stmt->execute()) {
                    $successCount++;
                }
                break;
        }
    }
    
    // Get updated stats
    $stats = $messageModel->getStats();
    
    echo json_encode([
        'success' => true,
        'message' => "Successfully processed {$successCount} out of " . count($messageIds) . " messages",
        'success_count' => $successCount,
        'total_count' => count($messageIds),
        'action' => $action,
        'unread_count' => $stats['unread'],
        'processed_ids' => $messageIds
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
