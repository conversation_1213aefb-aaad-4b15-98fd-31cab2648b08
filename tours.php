<?php
// Set performance headers first, before any output
require_once 'includes/performance.php';
Performance::setPerformanceHeaders();

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';

// Initialize models
$destinationModel = new Destination();
$tourPackageModel = new TourPackage();
$packageTypeModel = new TourPackageType();

// Fetch all destinations and packages
$allDestinations = $destinationModel->findAllWithImages();
$allPackages = $tourPackageModel->findAllWithDetails();
$packageTypes = $packageTypeModel->findAll();

// Handle filter requests
$searchTerm = $_GET['search'] ?? '';
$packageTypeFilter = $_GET['package_type'] ?? '';
$priceRangeFilter = $_GET['price_range'] ?? '';
$sortOption = $_GET['sort'] ?? 'default';

// Filter destinations
$filteredDestinations = array_filter($allDestinations, function($destination) use ($searchTerm, $priceRangeFilter) {
    $matchesSearch = empty($searchTerm) || 
        stripos($destination['name'], $searchTerm) !== false || 
        stripos($destination['short_description'], $searchTerm) !== false;
    
    $matchesPrice = true;
    if (!empty($priceRangeFilter)) {
        $priceRange = explode('-', $priceRangeFilter);
        $minPrice = $priceRange[0];
        $maxPrice = $priceRange[1] ?? PHP_FLOAT_MAX;
        $matchesPrice = $destination['price'] >= $minPrice && $destination['price'] <= $maxPrice;
    }
    
    return $matchesSearch && $matchesPrice;
});

// Filter packages
$filteredPackages = array_filter($allPackages, function($package) use ($searchTerm, $packageTypeFilter, $priceRangeFilter) {
    $matchesSearch = empty($searchTerm) || 
        stripos($package['name'], $searchTerm) !== false || 
        stripos($package['description'], $searchTerm) !== false;
    
    $matchesType = empty($packageTypeFilter) || 
        $package['package_type_id'] == $packageTypeFilter || 
        stripos($package['type_name'], $packageTypeFilter) !== false;
    
    $matchesPrice = true;
    if (!empty($priceRangeFilter)) {
        $priceRange = explode('-', $priceRangeFilter);
        $minPrice = $priceRange[0];
        $maxPrice = $priceRange[1] ?? PHP_FLOAT_MAX;
        $matchesPrice = $package['price'] >= $minPrice && $package['price'] <= $maxPrice;
    }
    
    return $matchesSearch && $matchesType && $matchesPrice;
});

// Sort results
if ($sortOption === 'price_low') {
    usort($filteredDestinations, function($a, $b) { return $a['price'] <=> $b['price']; });
    usort($filteredPackages, function($a, $b) { return $a['price'] <=> $b['price']; });
} elseif ($sortOption === 'price_high') {
    usort($filteredDestinations, function($a, $b) { return $b['price'] <=> $a['price']; });
    usort($filteredPackages, function($a, $b) { return $b['price'] <=> $a['price']; });
} elseif ($sortOption === 'name') {
    usort($filteredDestinations, function($a, $b) { return strcmp($a['name'], $b['name']); });
    usort($filteredPackages, function($a, $b) { return strcmp($a['name'], $b['name']); });
}

// Set SEO data for tours page
$seoTitle = 'All Tours & Destinations';
$seoDescription = 'Explore our complete collection of Kenya safari tours and destinations. From Maasai Mara wildlife safaris to coastal Kilifi adventures, find your perfect African tour package.';
$seoKeywords = 'Kenya tours, safari packages, African destinations, Maasai Mara tours, Kilifi tours, wildlife safari, Kenya travel packages, African adventure tours';
$seoImage = 'images/maasai-mara.jpg';
$seoType = 'website';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        /* Tours Page Specific Styles */
        .filter-section {
            transition: all 0.3s ease;
        }
        
        .filter-toggle {
            display: none;
        }
        
        .filter-toggle:checked ~ .filter-content {
            max-height: 1000px;
            opacity: 1;
            padding: 1.5rem;
        }
        
        .filter-content {
            max-height: 0;
            opacity: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            padding: 0 1.5rem;
        }
        
        .price-range-slider {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #f97316;
            outline: none;
        }
        
        .price-range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ea580c;
            cursor: pointer;
        }
        
        .price-range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ea580c;
            cursor: pointer;
        }
        
        .result-count {
            position: relative;
            padding-left: 1.5rem;
        }
        
        .result-count::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 60%;
            width: 4px;
            background: #f97316;
            border-radius: 2px;
        }
        
        .no-results {
            min-height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* Hero Section Styles */
        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(249, 115, 22, 0.3));
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .parallax-bg {
            background-attachment: fixed;
        }

        @media (max-width: 768px) {
            .parallax-bg {
                background-attachment: scroll;
            }
        }

        /* Tab Navigation Styles */
        .tab-navigation {
            border-bottom: 2px solid #f3f4f6;
        }

        .tab-button {
            position: relative;
            padding: 1rem 1.5rem;
            font-weight: 600;
            color: #6b7280;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            color: #f97316;
            background-color: #fef3e2;
        }

        .tab-button.active {
            color: #f97316;
            border-bottom-color: #f97316;
            background-color: #fef3e2;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tab-indicator {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 1.5rem;
            height: 1.5rem;
            background-color: #f97316;
            color: white;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="relative bg-cover bg-center bg-no-repeat parallax-bg py-20 px-4" style="background-image: url('images/nav-bg.jpg')">
        <div class="absolute inset-0 hero-overlay"></div>

        <!-- Hero Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center text-white">
            <h1 class="text-3xl md:text-5xl font-bold mb-6 text-shadow">
                Explore Our <span class="text-orange-400">Tours & Destinations</span>
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8"></div>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto text-shadow">
                Discover all our available destinations and tour packages. Filter to find your perfect adventure.
            </p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-12 px-4">
        <div class="max-w-7xl mx-auto">
            
            <!-- Filter Section -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-12 filter-section">
                <label for="filter-toggle" class="block p-6 cursor-pointer">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg md:text-xl font-semibold text-gray-800">
                            <i class="fas fa-filter text-orange-500 mr-2"></i> Filter Options
                        </h2>
                        <svg class="w-6 h-6 text-orange-500 transition-transform transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </label>
                
                <input type="checkbox" id="filter-toggle" class="filter-toggle hidden">
                
                <div class="filter-content bg-gray-50">
                    <form method="get" action="tours.php" class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- Search Filter -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                            <div class="relative">
                                <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>" 
                                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500" 
                                       placeholder="Search destinations or packages...">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Package Type Filter -->
                        <div>
                            <label for="package_type" class="block text-sm font-medium text-gray-700 mb-2">Package Type</label>
                            <select id="package_type" name="package_type" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500">
                                <option value="">All Types</option>
                                <?php foreach ($packageTypes as $type): ?>
                                    <option value="<?php echo $type['package_type_id']; ?>" <?php echo $packageTypeFilter == $type['package_type_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($type['type_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Price Range Filter -->
                        <div>
                            <label for="price_range" class="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                            <select id="price_range" name="price_range" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500">
                                <option value="">All Prices</option>
                                <option value="0-500" <?php echo $priceRangeFilter === '0-500' ? 'selected' : ''; ?>>Under $500</option>
                                <option value="500-1000" <?php echo $priceRangeFilter === '500-1000' ? 'selected' : ''; ?>>$500 - $1,000</option>
                                <option value="1000-2000" <?php echo $priceRangeFilter === '1000-2000' ? 'selected' : ''; ?>>$1,000 - $2,000</option>
                                <option value="2000-5000" <?php echo $priceRangeFilter === '2000-5000' ? 'selected' : ''; ?>>$2,000 - $5,000</option>
                                <option value="5000-" <?php echo $priceRangeFilter === '5000-' ? 'selected' : ''; ?>>Over $5,000</option>
                            </select>
                        </div>
                        
                        <!-- Sort Options -->
                        <div>
                            <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                            <select id="sort" name="sort" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500">
                                <option value="default" <?php echo $sortOption === 'default' ? 'selected' : ''; ?>>Default</option>
                                <option value="price_low" <?php echo $sortOption === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                                <option value="price_high" <?php echo $sortOption === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                                <option value="name" <?php echo $sortOption === 'name' ? 'selected' : ''; ?>>Name: A-Z</option>
                            </select>
                        </div>
                        
                        <!-- Filter Buttons -->
                        <div class="md:col-span-3 flex flex-col sm:flex-row gap-4 justify-end">
                            <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition duration-300">
                                Apply Filters
                            </button>
                            <a href="tours.php" class="border border-gray-300 hover:bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-medium transition duration-300 text-center">
                                Reset Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="bg-white rounded-xl shadow-sm mb-8 overflow-hidden">
                <div class="tab-navigation">
                    <div class="flex flex-wrap">
                        <button class="tab-button active" data-tab="all">
                            <i class="fas fa-globe mr-2"></i>
                            All Results
                            <span class="tab-indicator"><?php echo count($filteredDestinations) + count($filteredPackages); ?></span>
                        </button>
                        <button class="tab-button" data-tab="destinations">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Destinations
                            <span class="tab-indicator"><?php echo count($filteredDestinations); ?></span>
                        </button>
                        <button class="tab-button" data-tab="packages">
                            <i class="fas fa-suitcase-rolling mr-2"></i>
                            Tour Packages
                            <span class="tab-indicator"><?php echo count($filteredPackages); ?></span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Summary -->
            <div class="flex flex-wrap justify-between items-center mb-8">
                <h3 class="text-lg font-semibold text-gray-800 result-count">
                    <span id="results-summary">Showing <?php echo count($filteredDestinations); ?> destinations and <?php echo count($filteredPackages); ?> packages</span>
                </h3>
                <div class="text-sm text-gray-500">
                    <span id="total-results">Found <?php echo count($filteredDestinations) + count($filteredPackages); ?> total results</span>
                </div>
            </div>
            
            <!-- Tab Content Container -->
            <div class="tab-content-container">
                <!-- All Results Tab Content -->
                <div class="tab-content active" id="all-content">
                    <!-- Destinations Section -->
                    <section class="mb-16" id="destinations">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">Destinations</h2>
                
                <?php if (empty($filteredDestinations)): ?>
                    <div class="bg-white rounded-xl shadow-sm p-8 no-results">
                        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-lg md:text-xl text-gray-600 mb-2">No destinations match your filters</p>
                        <p class="text-gray-500">Try adjusting your search criteria or <a href="tours.php" class="text-orange-500 hover:underline">reset all filters</a></p>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($filteredDestinations as $destination): ?>
                            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                <?php if ($destination['display_image_url']): ?>
                                    <img src="admin-dashboard/<?php echo htmlspecialchars($destination['display_image_url']); ?>" 
                                         alt="<?php echo htmlspecialchars($destination['name']); ?>" 
                                         class="w-full h-60 object-cover">
                                <?php else: ?>
                                    <div class="w-full h-60 bg-gray-200 flex items-center justify-center">
                                        <div class="text-center text-gray-500">
                                            <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <p class="text-sm">No Image Available</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="relative p-6 pt-2 h-32">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                        <?php echo Utils::displayText($destination['name']); ?>
                                    </h3>
                                    <p class="text-gray-700 mb-4 text-sm line-clamp-2">
                                        <?php
                                        $description = $destination['short_description'] ?: 'Discover this amazing destination with unique experiences and unforgettable memories.';
                                        echo Utils::displayText($description);
                                        ?>
                                    </p>
                                    <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                        <span class="text-base font-semibold gradient-text">
                                            <?php if ($destination['price'] > 0): ?>
                                                From $<?php echo number_format($destination['price']); ?>
                                            <?php else: ?>
                                                From $0
                                            <?php endif; ?>
                                        </span>
                                        <a href="destination-details.php?id=<?php echo $destination['destination_id']; ?>&from=tours"
                                           class="text-orange-500 hover:text-orange-600 font-medium transition duration-300">
                                            Learn More →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                    </section>

                    <!-- Tour Packages Section -->
                    <section id="packages">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">Tour Packages</h2>
                
                <?php if (empty($filteredPackages)): ?>
                    <div class="bg-white rounded-xl shadow-sm p-8 no-results">
                        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-lg md:text-xl text-gray-600 mb-2">No tour packages match your filters</p>
                        <p class="text-gray-500">Try adjusting your search criteria or <a href="tours.php" class="text-orange-500 hover:underline">reset all filters</a></p>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($filteredPackages as $package): ?>
                            <div class="bg-gray-900 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                <?php if ($package['display_image_url']): ?>
                                    <img src="admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?>"
                                         alt="<?php echo htmlspecialchars($package['name']); ?>"
                                         class="w-full h-60 object-cover">
                                <?php else: ?>
                                    <div class="w-full h-60 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <p class="text-sm">Coming Soon</p>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div class="relative pt-4 p-6 h-48">
                                    <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                        <?php
                                        // Smart formatting based on package type and duration (same as index.php)
                                        $packageName = $package['name'];
                                        $tourType = $package['type_name'] ?: 'Tour';
                                        $duration = $package['duration'];

                                        // Check if tour type already implies duration
                                        $lowerTourType = strtolower($tourType);
                                        $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                                            strpos($lowerTourType, 'overnight') !== false ||
                                                            strpos($lowerTourType, 'night') !== false);

                                        if ($typeImpliesDuration) {
                                            // Format: Tour Type + Package Name
                                            $formattedName = $tourType . ' ' . $packageName;
                                        } elseif (!empty($duration)) {
                                            // Format: Duration + Package Name + Tour Type
                                            $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                        } else {
                                            // Format: Package Name + Tour Type
                                            $formattedName = $packageName . ' ' . $tourType;
                                        }

                                        echo Utils::displayText($formattedName);
                                        ?>
                                    </h3>
                                    <p class="text-gray-200 mb-4 text-base line-clamp-2">
                                        <?php
                                        $description = $package['description'] ?: 'Discover this amazing tour package with unique experiences and unforgettable memories.';
                                        echo Utils::displayText($description);
                                        ?>
                                    </p>
                                    <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                        <span class="text-base font-semibold text-orange-400">
                                            $<?php echo number_format($package['price']); ?>
                                        </span>
                                        <a href="package-details.php?id=<?php echo $package['tour_package_id']; ?>&from=tours"
                                           class="text-sm bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-full font-medium transition duration-300 flex items-center">
                                            View Details <i class="fas fa-arrow-right ml-2"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                    </section>
                </div>

                <!-- Destinations Only Tab Content -->
                <div class="tab-content" id="destinations-content">
                    <section class="mb-16">
                        <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">Destinations</h2>

                        <?php if (empty($filteredDestinations)): ?>
                            <div class="bg-white rounded-xl shadow-sm p-8 no-results">
                                <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-lg text-gray-600 mb-2">No destinations match your filters</p>
                                <p class="text-gray-500">Try adjusting your search criteria or <a href="tours.php" class="text-orange-500 hover:underline">reset all filters</a></p>
                            </div>
                        <?php else: ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <?php foreach ($filteredDestinations as $destination): ?>
                                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                        <?php if ($destination['display_image_url']): ?>
                                            <img src="admin-dashboard/<?php echo htmlspecialchars($destination['display_image_url']); ?>"
                                                 alt="<?php echo htmlspecialchars($destination['name']); ?>"
                                                 class="w-full h-60 object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-60 bg-gray-200 flex items-center justify-center">
                                                <div class="text-center text-gray-500">
                                                    <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                    <p class="text-sm">No Image Available</p>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="relative p-6 pt-2 h-32">
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                                <?php echo Utils::displayText($destination['name']); ?>
                                            </h3>
                                            <p class="text-gray-700 mb-4 text-sm line-clamp-2">
                                                <?php
                                                $description = $destination['short_description'] ?: 'Discover this amazing destination with unique experiences and unforgettable memories.';
                                                echo Utils::displayText($description);
                                                ?>
                                            </p>
                                            <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                                <span class="text-base font-semibold gradient-text">
                                                    <?php if ($destination['price'] > 0): ?>
                                                        From $<?php echo number_format($destination['price']); ?>
                                                    <?php else: ?>
                                                        From $0
                                                    <?php endif; ?>
                                                </span>
                                                <a href="destination-details.php?id=<?php echo $destination['destination_id']; ?>&from=tours"
                                                   class="text-orange-500 hover:text-orange-600 font-medium transition duration-300">
                                                    Learn More →
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </section>
                </div>

                <!-- Packages Only Tab Content -->
                <div class="tab-content" id="packages-content">
                    <section>
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Tour Packages</h2>

                        <?php if (empty($filteredPackages)): ?>
                            <div class="bg-white rounded-xl shadow-sm p-8 no-results">
                                <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-lg text-gray-600 mb-2">No tour packages match your filters</p>
                                <p class="text-gray-500">Try adjusting your search criteria or <a href="tours.php" class="text-orange-500 hover:underline">reset all filters</a></p>
                            </div>
                        <?php else: ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <?php foreach ($filteredPackages as $package): ?>
                                    <div class="bg-gray-900 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                        <?php if ($package['display_image_url']): ?>
                                            <img src="admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?>"
                                                 alt="<?php echo htmlspecialchars($package['name']); ?>"
                                                 class="w-full h-60 object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-60 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                                <div class="text-center text-white">
                                                    <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                    <p class="text-sm">Coming Soon</p>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="relative pt-4 p-6 h-48">
                                            <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                                <?php
                                                // Smart formatting based on package type and duration (same as index.php)
                                                $packageName = $package['name'];
                                                $tourType = $package['type_name'] ?: 'Tour';
                                                $duration = $package['duration'];

                                                // Check if tour type already implies duration
                                                $lowerTourType = strtolower($tourType);
                                                $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                                                    strpos($lowerTourType, 'overnight') !== false ||
                                                                    strpos($lowerTourType, 'night') !== false);

                                                if ($typeImpliesDuration) {
                                                    // Format: Tour Type + Package Name
                                                    $formattedName = $tourType . ' ' . $packageName;
                                                } elseif (!empty($duration)) {
                                                    // Format: Duration + Package Name + Tour Type
                                                    $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                                } else {
                                                    // Format: Package Name + Tour Type
                                                    $formattedName = $packageName . ' ' . $tourType;
                                                }

                                                echo Utils::displayText($formattedName);
                                                ?>
                                            </h3>
                                            <p class="text-gray-200 mb-4 text-base line-clamp-2">
                                                <?php
                                                $description = $package['description'] ?: 'Discover this amazing tour package with unique experiences and unforgettable memories.';
                                                echo Utils::displayText($description);
                                                ?>
                                            </p>
                                            <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                                <span class="text-base font-semibold text-orange-400">
                                                    $<?php echo number_format($package['price']); ?>
                                                </span>
                                                <a href="package-details.php?id=<?php echo $package['tour_package_id']; ?>&from=tours"
                                                   class="text-sm bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-full font-medium transition duration-300 flex items-center">
                                                    View Details <i class="fas fa-arrow-right ml-2"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </section>
                </div>
            </div>
        </div>
    </main>

    <!-- Call to Action Section -->
    <section class="relative py-20 px-4 text-white overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image: url('images/cta-banner.jpg'); filter: blur(2px);">
            <!-- Dark Overlay for better text readability -->
            <div class="absolute inset-0 bg-black bg-opacity-80"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold mb-6 text-white drop-shadow-lg">
                Ready for Your African Adventure?
            </h2>
            <p class="text-lg md:text-xl mb-10 text-white drop-shadow-md max-w-3xl mx-auto leading-relaxed">
                Found something that caught your eye? Let us create a personalized quote for your perfect safari experience.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="request-quote.php" class="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                    Get Your Custom Quote
                </a>
                <a href="contact.php" class="border-2 border-white text-white hover:bg-white hover:text-orange-500 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl">
                    Speak with an Expert
                </a>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 border-2 border-white opacity-20 rounded-full"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 border-2 border-orange-400 opacity-30 rounded-full"></div>
        <div class="absolute top-1/2 left-5 w-3 h-3 bg-orange-400 opacity-40 rounded-full"></div>
        <div class="absolute top-1/4 right-20 w-2 h-2 bg-white opacity-50 rounded-full"></div>
    </section>

    <!-- Include Footer -->
    <?php include 'footer.php'; ?>

    <script>
        // Initialize filter toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterToggle = document.getElementById('filter-toggle');
            const filterIcon = document.querySelector('label[for="filter-toggle"] svg');

            filterToggle.addEventListener('change', function() {
                if (this.checked) {
                    filterIcon.classList.add('rotate-180');
                } else {
                    filterIcon.classList.remove('rotate-180');
                }
            });

            // Preserve filter state on page load if filters are active
            <?php if (!empty($searchTerm) || !empty($packageTypeFilter) || !empty($priceRangeFilter) || $sortOption !== 'default'): ?>
                filterToggle.checked = true;
                filterIcon.classList.add('rotate-180');
                document.querySelector('.filter-content').style.maxHeight = '1000px';
                document.querySelector('.filter-content').style.opacity = '1';
                document.querySelector('.filter-content').style.padding = '1.5rem';
            <?php endif; ?>

            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            const resultsSummary = document.getElementById('results-summary');
            const totalResults = document.getElementById('total-results');

            // Tab content data
            const tabData = {
                'all': {
                    summary: 'Showing <?php echo count($filteredDestinations); ?> destinations and <?php echo count($filteredPackages); ?> packages',
                    total: 'Found <?php echo count($filteredDestinations) + count($filteredPackages); ?> total results'
                },
                'destinations': {
                    summary: 'Showing <?php echo count($filteredDestinations); ?> destinations',
                    total: 'Found <?php echo count($filteredDestinations); ?> destinations'
                },
                'packages': {
                    summary: 'Showing <?php echo count($filteredPackages); ?> tour packages',
                    total: 'Found <?php echo count($filteredPackages); ?> packages'
                }
            };

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all tabs and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show corresponding content
                    const targetContent = document.getElementById(targetTab + '-content');
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }

                    // Update results summary
                    if (tabData[targetTab]) {
                        resultsSummary.textContent = tabData[targetTab].summary;
                        totalResults.textContent = tabData[targetTab].total;
                    }

                    // Update URL hash for deep linking
                    if (targetTab === 'destinations') {
                        window.history.replaceState(null, null, '#destinations');
                    } else if (targetTab === 'packages') {
                        window.history.replaceState(null, null, '#packages');
                    } else {
                        window.history.replaceState(null, null, window.location.pathname + window.location.search);
                    }
                });
            });

            // Handle deep linking from URL hash
            const hash = window.location.hash;
            if (hash === '#destinations') {
                document.querySelector('[data-tab="destinations"]').click();
            } else if (hash === '#packages') {
                document.querySelector('[data-tab="packages"]').click();
            }
        });
    </script>
</body>
</html>