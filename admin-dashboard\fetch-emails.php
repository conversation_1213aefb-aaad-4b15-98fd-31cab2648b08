<?php
/**
 * Manual Email Fetcher
 * Run this script to manually fetch emails from IMAP server
 * Can be run via browser or command line
 */

// Set execution time limit for email processing
set_time_limit(300); // 5 minutes

require_once 'config/config.php';
require_once 'classes/ImapEmailFetcher.php';

// Check if running from command line or browser
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    // Browser access - add basic security
    session_start();
    
    // Simple authentication check (you can enhance this)
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        // For testing purposes, allow access with a simple password
        if (!isset($_GET['key']) || $_GET['key'] !== 'fetch_emails_2024') {
            die('Access denied. Add ?key=fetch_emails_2024 to URL for testing.');
        }
    }
    
    header('Content-Type: text/html; charset=utf-8');
    echo "<h2>Email <PERSON> - Meleva Tours Admin</h2>\n";
    echo "<p>Starting email fetch process...</p>\n";
    echo "<pre>\n";
    flush();
}

try {
    $fetcher = new ImapEmailFetcher();
    
    if ($isCLI) {
        echo "Starting email fetch process...\n";
    }
    
    // Fetch emails from all accounts
    $results = $fetcher->fetchAllEmails();
    
    // Display results
    foreach ($results as $email => $result) {
        echo "\n=== Results for $email ===\n";
        
        if (isset($result['error'])) {
            echo "ERROR: " . $result['error'] . "\n";
        } else {
            echo "Processed: {$result['processed']} emails\n";
            echo "Total found: {$result['total_found']} emails\n";
            
            if (!empty($result['errors'])) {
                echo "Errors encountered:\n";
                foreach ($result['errors'] as $error) {
                    echo "  - $error\n";
                }
            }
        }
    }
    
    $totalProcessed = array_sum(array_column(array_filter($results, function($r) { 
        return !isset($r['error']); 
    }), 'processed'));
    
    echo "\n=== SUMMARY ===\n";
    echo "Total emails processed: $totalProcessed\n";
    echo "Fetch completed at: " . date('Y-m-d H:i:s') . "\n";
    
    if (!$isCLI) {
        echo "</pre>\n";
        echo "<p><strong>Email fetch completed!</strong></p>\n";
        echo "<p><a href='messages.php'>View Messages</a> | <a href='fetch-emails.php?key=fetch_emails_2024'>Fetch Again</a></p>\n";
    }
    
} catch (Exception $e) {
    $errorMsg = "Email fetch failed: " . $e->getMessage();
    error_log($errorMsg);
    
    if ($isCLI) {
        echo "ERROR: $errorMsg\n";
    } else {
        echo "</pre>\n";
        echo "<p style='color: red;'><strong>ERROR:</strong> $errorMsg</p>\n";
    }
}

// Log the fetch attempt
error_log("Email fetch completed at " . date('Y-m-d H:i:s') . " - Total processed: " . ($totalProcessed ?? 0));
?>
