<?php
// Set performance headers first, before any output
require_once 'includes/performance.php';
Performance::setPerformanceHeaders();

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/booking_models.php';

// Initialize models
$paymentModel = new Payment();
$bookingModel = new Booking();
$quoteModel = new Quote();

// Get payment ID from URL or quote reference
$paymentId = $_GET['payment_id'] ?? null;
$quoteRef = $_GET['quote_ref'] ?? null;
$payment = null;
$booking = null;
$quote = null;
$message = '';

if ($paymentId) {
    // Handle traditional payment ID approach
    $payment = $paymentModel->findWithBookingDetails($paymentId);

    if (!$payment) {
        $message = 'Payment not found.';
    } else {
        if ($payment['booking_id']) {
            // Get full booking details for booking-based payment
            $booking = $bookingModel->findWithDetails($payment['booking_id']);
        } elseif ($payment['quote_id']) {
            // Get quote details for quote-based payment
            $quote = $quoteModel->findById($payment['quote_id']);
        }
    }
} elseif ($quoteRef) {
    // Handle simplified quote reference approach
    $quote = $quoteModel->findByReference($quoteRef);

    if (!$quote) {
        $message = 'Quote not found.';
    }
} else {
    $message = 'Payment ID or quote reference is required.';
}

// Payment pages should not be indexed by search engines
$noIndex = true;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-50">
    <?php include 'header.php'; ?>

    <!-- Failure Section -->
    <section class="py-16">
        <div class="max-w-4xl mx-auto px-6">
            
            <?php if ($message): ?>
                <div class="text-center">
                    <div class="bg-red-100 text-red-800 border border-red-200 p-6 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <h2 class="text-2xl font-bold mb-2">Error</h2>
                        <p><?php echo htmlspecialchars($message); ?></p>
                        <div class="mt-6">
                            <a href="index.php" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                                <i class="fas fa-home mr-2"></i>Return Home
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Failure Message -->
                <div class="text-center mb-8">
                    <div class="bg-red-100 text-red-800 border border-red-200 p-8 rounded-lg">
                        <i class="fas fa-times-circle text-6xl mb-6 text-red-600"></i>
                        <h1 class="text-4xl font-bold mb-4">Payment Failed</h1>
                        <p class="text-xl mb-6">Unfortunately, your payment could not be processed at this time.</p>
                        
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Payment Details</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                <div>
                                    <?php if ($payment): ?>
                                        <p><strong>Payment Reference:</strong> <?php echo htmlspecialchars($payment['payment_reference']); ?></p>
                                        <p><strong>Reference:</strong> <?php echo htmlspecialchars($payment['booking_reference'] ?? $quote['quote_reference']); ?></p>
                                        <p><strong>Amount:</strong> $<?php echo number_format($payment['amount'], 2); ?></p>
                                    <?php elseif ($quote): ?>
                                        <p><strong>Quote Reference:</strong> <?php echo htmlspecialchars($quote['quote_reference']); ?></p>
                                        <p><strong>Amount:</strong> $<?php echo number_format($quote['quoted_amount'], 2); ?></p>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if ($payment): ?>
                                        <p><strong>Payment Type:</strong> <?php echo ucfirst($payment['payment_type']); ?> Payment</p>
                                        <p><strong>Attempted:</strong> <?php echo date('F j, Y g:i A', strtotime($payment['created_at'])); ?></p>
                                    <?php endif; ?>
                                    <p><strong>Status:</strong> <span class="text-red-600 font-semibold">Failed</span></p>
                                </div>
                            </div>

                            <?php if ($payment && $payment['pesapal_status']): ?>
                                <div class="mt-4 pt-4 border-t">
                                    <p><strong>Failure Reason:</strong> <?php echo htmlspecialchars($payment['pesapal_status']); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Common Reasons -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-8 mb-8">
                    <h2 class="text-2xl font-bold text-yellow-800 mb-4">
                        <i class="fas fa-lightbulb mr-2"></i>Common Reasons for Payment Failure
                    </h2>
                    <div class="space-y-3 text-yellow-700">
                        <p><i class="fas fa-credit-card mr-2"></i>Insufficient funds in your account</p>
                        <p><i class="fas fa-ban mr-2"></i>Card declined by your bank</p>
                        <p><i class="fas fa-wifi mr-2"></i>Network connectivity issues</p>
                        <p><i class="fas fa-lock mr-2"></i>Security restrictions on your card</p>
                        <p><i class="fas fa-clock mr-2"></i>Payment session timeout</p>
                        <p><i class="fas fa-exclamation-triangle mr-2"></i>Incorrect payment details entered</p>
                    </div>
                </div>

                <?php if ($booking): ?>
                    <!-- Booking Summary -->
                    <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-6">Your Booking Details</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-3">Customer Information</h3>
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($booking['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($booking['customer_email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($booking['customer_phone']); ?></p>
                            </div>
                            
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-3">Travel Details</h3>
                                <p><strong>Travel Date:</strong> <?php echo date('F j, Y', strtotime($booking['travel_date'])); ?></p>
                                <p><strong>Adults:</strong> <?php echo $booking['number_of_adults']; ?></p>
                                <p><strong>Children:</strong> <?php echo $booking['number_of_children']; ?></p>
                            </div>
                        </div>
                        
                        <?php if (!empty($booking['package_names'])): ?>
                            <div class="border-t pt-6">
                                <h3 class="font-semibold text-gray-700 mb-3">Selected Packages</h3>
                                <p class="text-gray-600"><?php echo htmlspecialchars($booking['package_names']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="border-t pt-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="text-lg"><strong>Total Booking Amount:</strong> KES <?php echo number_format($booking['total_amount'], 2); ?></p>
                                    <p class="text-lg text-red-600"><strong>Payment Failed:</strong> KES <?php echo number_format($payment['amount'], 2); ?></p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm text-gray-600">Booking Status:</span><br>
                                    <span class="text-lg font-semibold text-orange-600">
                                        <?php echo ucfirst($booking['booking_status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Next Steps -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-8 mb-8">
                    <h2 class="text-2xl font-bold text-blue-800 mb-4">
                        <i class="fas fa-question-circle mr-2"></i>What Should You Do Next?
                    </h2>
                    <div class="space-y-3 text-blue-700">
                        <p><i class="fas fa-redo mr-2"></i>Try making the payment again using the button below</p>
                        <p><i class="fas fa-credit-card mr-2"></i>Check with your bank if your card has any restrictions</p>
                        <p><i class="fas fa-phone mr-2"></i>Contact our support team for assistance</p>
                        <p><i class="fas fa-money-bill-alt mr-2"></i>Try using a different payment method</p>
                        <p><i class="fas fa-clock mr-2"></i>Your booking is still reserved - payment can be completed later</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center space-y-4">
                    <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                        <?php if ($payment && $payment['booking_id']): ?>
                            <a href="payment.php?payment_id=<?php echo $paymentId; ?>"
                               class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                                <i class="fas fa-redo mr-2"></i>Try Payment Again
                            </a>
                        <?php elseif ($quote): ?>
                            <a href="payment.php?quote_ref=<?php echo urlencode($quote['quote_reference']); ?>"
                               class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                                <i class="fas fa-redo mr-2"></i>Try Payment Again
                            </a>
                        <?php endif; ?>
                        
                        <a href="contact.php" 
                           class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                            <i class="fas fa-headset mr-2"></i>Contact Support
                        </a>
                        
                        <a href="index.php" 
                           class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                            <i class="fas fa-home mr-2"></i>Return Home
                        </a>
                    </div>
                    
                    <div class="mt-8 p-4 bg-gray-100 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Need Immediate Help?</h3>
                        <p class="text-sm text-gray-600 mb-3">
                            Our support team is available to help you complete your payment
                        </p>
                        <div class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-6">
                            <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700 font-medium">
                                <i class="fas fa-envelope mr-1"></i><EMAIL>
                            </a>
                            <a href="tel:+254123456789" class="text-orange-600 hover:text-orange-700 font-medium">
                                <i class="fas fa-phone mr-1"></i>+254 123 456 789
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php include 'footer.php'; ?>
</body>
</html>
