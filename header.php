<?php
// Include database configuration and models for mega menu
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'includes/seo-meta.php';

// Get current page name for active navigation highlighting
$currentPage = basename($_SERVER['PHP_SELF']);

// Define pages that have hero sections (full-height or prominent hero banners)
$heroPages = ['index.php'];
$hasHeroSection = in_array($currentPage, $heroPages);

// Initialize models for mega menu data
try {
    $headerTourPackageModel = new TourPackage();
    $headerPackageTypeModel = new TourPackageType();
    $headerDestinationModel = new Destination();

    // Fetch data for mega menu
    $headerPackageTypes = $headerPackageTypeModel->findAll();
    $headerAllPackages = $headerTourPackageModel->findAllWithDetails();
    $headerDestinations = $headerDestinationModel->findAllWithImages();

    // Group packages by type for mega menu
    $headerPackagesByType = [];
    foreach ($headerAllPackages as $headerPackage) {
        $typeId = $headerPackage['package_type_id'];
        if (!isset($headerPackagesByType[$typeId])) {
            $headerPackagesByType[$typeId] = [];
        }
        $headerPackagesByType[$typeId][] = $headerPackage;
    }

} catch (Exception $e) {
    // Fallback data if database connection fails
    $headerPackageTypes = [];
    $headerAllPackages = [];
    $headerDestinations = [];
    $headerPackagesByType = [];
    error_log("Header mega menu data fetch error: " . $e->getMessage());
}
?>

<head>
    <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="apple-touch-icon" href="favicon.png">

    <?php
    // Initialize SEO Meta helper
    $seoMeta = new SEOMeta();

    // Allow pages to override SEO data by setting these variables before including header
    if (!isset($seoTitle)) $seoTitle = 'Authentic African Safari Adventures';
    if (!isset($seoDescription)) $seoDescription = 'Experience authentic African safari adventures with Meleva Tours & Travel. Discover Kenya\'s wildlife, stunning landscapes, and cultural heritage through our expertly crafted tour packages.';
    if (!isset($seoKeywords)) $seoKeywords = 'Kenya safari, African tours, wildlife safari, Maasai Mara, Kilifi tours, Kenya travel, safari packages, African adventure, wildlife photography, cultural tours';
    if (!isset($seoImage)) $seoImage = '';
    if (!isset($seoType)) $seoType = 'website';

    // Generate meta tags
    $seoMeta->generateMetaTags([
        'title' => $seoTitle,
        'description' => $seoDescription,
        'keywords' => $seoKeywords,
        'image' => $seoImage,
        'type' => $seoType,
        'noIndex' => $noIndex ?? false
    ]);

    // Performance optimizations
    // Generate resource hints
    echo Performance::generateResourceHints();

    // Generate critical CSS
    echo Performance::generateCriticalCSS();

    // Page-specific preloads
    $pageType = 'default';
    if (basename($_SERVER['PHP_SELF']) === 'index.php') $pageType = 'home';
    elseif (basename($_SERVER['PHP_SELF']) === 'tours.php') $pageType = 'tours';
    elseif (basename($_SERVER['PHP_SELF']) === 'gallery.php') $pageType = 'gallery';
    elseif (in_array(basename($_SERVER['PHP_SELF']), ['destination-details.php', 'package-details.php'])) $pageType = 'details';

    echo Performance::generatePageSpecificPreloads($pageType);
    ?>

    <style>
        /* Navigation Styles */
        #main-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Initial state - transparent for hero pages, solid for others */
        #main-nav.has-hero-section {
            background-color: transparent;
            box-shadow: none;
        }

        #main-nav.no-hero-section {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* Scrolled state */
        #main-nav.scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        /* Visibility states */
        #main-nav.nav-visible {
            transform: translateY(0);
            opacity: 1;
        }

        #main-nav.nav-hidden {
            transform: translateY(-100%);
            opacity: 0;
        }

        /* Text colors for transparent state */
        #main-nav.has-hero-section:not(.scrolled) .nav-link:not(.text-orange-400),
        #main-nav.has-hero-section:not(.scrolled) #mobile-menu-toggle {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Text colors for solid/scrolled state */
        #main-nav.no-hero-section .nav-link:not(.text-orange-400),
        #main-nav.no-hero-section #mobile-menu-toggle,
        #main-nav.scrolled .nav-link:not(.text-orange-400),
        #main-nav.scrolled #mobile-menu-toggle {
            color: #4b5563 !important;
            text-shadow: none;
        }

        /* Enhanced Hover effects */
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link:hover:not(.text-orange-400) {
            color: #f97316 !important;
            transform: translateY(-2px);
        }

        /* Hover underline effect */
        .nav-link:not(.text-orange-400)::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #f97316, #ea580c);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:not(.text-orange-400):hover::after {
            width: 100%;
        }

        /* Active state enhancement */
        .nav-link.text-orange-400:hover {
            transform: translateY(-1px);
        }

        /* Active state */
        .nav-link.text-orange-400 {
            color: #fb923c !important;
        }

        /* Mobile menu button */
        #mobile-menu-toggle {
            transition: all 0.3s ease;
        }

        #mobile-menu-toggle:hover {
            transform: scale(1.05);
            color: #f97316 !important;
        }

        /* Mobile menu link hover effects */
        #mobile-menu .block:hover {
            transform: translateX(8px);
            padding-left: 1rem;
        }

        /* Mobile menu styles */
        #mobile-menu {
            transition: opacity 0.3s ease;
        }

        #mobile-menu.show {
            opacity: 1;
            visibility: visible;
        }

        #mobile-menu .mobile-menu-panel {
            transition: transform 0.3s ease-in-out;
        }

        #mobile-menu.show .mobile-menu-panel {
            transform: translateY(0);
        }

        /* Body padding for pages without hero sections */
        body.no-hero-section {
            padding-top: 80px;
        }

        /* Ensure hero pages start from top */
        body.has-hero-section {
            padding-top: 0;
        }

        /* Mega Menu Styles */
        .mega-menu {
            position: fixed;
            top: 80px; /* Position below the navbar height */
            left: 50%;
            transform: translateX(-50%) translateY(-10px);
            max-width: 80rem; /* max-w-7xl equivalent */
            width: 100%;
            background: linear-gradient(135deg, #f97316, #ea580c);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            border-top: 3px solid #dc2626;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            max-height: 80vh;
            overflow-y: auto;
        }

        /* Adjust mega menu position dynamically based on navbar state */
        .mega-menu.navbar-scrolled {
            top: 80px; /* Standard navbar height when scrolled */
        }

        .mega-menu.navbar-transparent {
            top: 80px; /* Same height for transparent navbar */
        }

        .mega-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
        }

        .mega-menu-content {
            padding: 2rem 1.5rem; /* px-6 equivalent */
        }

        .mega-menu-section {
            margin-bottom: 2rem;
        }

        .mega-menu-section:last-child {
            margin-bottom: 0;
        }

        .mega-menu-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            display: block;
            text-decoration: none;
            transition: all 0.2s ease;
            text-align: center;
            width: 100%;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .mega-menu-title:hover {
            color: #fef3c7;
            border-bottom-color: #fef3c7;
            transform: translateY(-1px);
        }

        .mega-menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .mega-menu-column {
            /* No background or borders - just clean layout */
        }

        .mega-menu-column-title {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .mega-menu-column-title:hover {
            color: #fef3c7;
            transform: translateX(4px);
        }

        .mega-menu-column-title i {
            margin-right: 0.5rem;
            color: #fef3c7;
        }

        .mega-menu-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .mega-menu-items li {
            margin-bottom: 0.25rem;
        }

        .mega-menu-items li:last-child {
            margin-bottom: 0;
        }

        .mega-menu-items a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            display: block;
            padding: 0.125rem 0;
            line-height: 1.4;
            border-radius: 4px;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        .mega-menu-items a:hover {
            color: #fef3c7;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .destinations-list {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
        }

        .destinations-column {
            /* No background or borders - just clean layout */
        }

        .destinations-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .destinations-items li {
            margin-bottom: 0.25rem;
        }

        .destinations-items li:last-child {
            margin-bottom: 0;
        }

        .destinations-items a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            display: block;
            padding: 0.125rem 0;
            line-height: 1.4;
            border-radius: 4px;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        .destinations-items a:hover {
            color: #fef3c7;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        /* Main navigation container with mega menu positioning */
        .main-nav-container {
            position: relative;
        }

        /* Navigation item with mega menu */
        .nav-item-with-mega {
            position: relative;
        }

        /* Responsive adjustments for mega menu */
        @media (max-width: 1200px) {
            .mega-menu-content {
                padding: 1.5rem;
            }

            .mega-menu-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }

            .destinations-list {
                grid-template-columns: repeat(4, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 1024px) {
            .mega-menu {
                display: none; /* Hide mega menu on tablet and mobile, use mobile menu instead */
            }
        }

        /* Ensure mega menu doesn't interfere with mobile menu */
        @media (max-width: 1023px) {
            .nav-item-with-mega .mega-menu {
                display: none !important;
            }
        }
    </style>
</head>

<!-- Main Navigation -->
<nav id="main-nav" class="w-full <?php echo $hasHeroSection ? 'has-hero-section' : 'no-hero-section'; ?> nav-visible" data-has-hero="<?php echo $hasHeroSection ? 'true' : 'false'; ?>">
    <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="main-nav-container">
            <div class="flex items-center justify-between">
                <div class="w-16">
                    <a href="index.php"><img src="images/meleva-lg.png" alt="Meleva Tours Logo" class="logo critical-image" loading="eager" decoding="sync"></a>
                </div>

                <ul class="hidden lg:flex gap-8 font-medium items-center">
                    <li><a href="index.php" class="nav-link <?php echo ($currentPage == 'index.php') ? 'text-orange-400 border-b-2 border-orange-400' : ''; ?> transition duration-300">Home</a></li>
                    <li><a href="about.php" class="nav-link <?php echo ($currentPage == 'about.php') ? 'text-orange-400 border-b-2 border-orange-400' : ''; ?> transition duration-300">About Us</a></li>
                    <li class="nav-item-with-mega">
                        <a href="tours.php" class="nav-link <?php echo ($currentPage == 'tours.php') ? 'text-orange-400 border-b-2 border-orange-400' : ''; ?> transition duration-300" id="tours-nav-link">
                            Tours & Destinations
                            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-300" id="tours-chevron"></i>
                        </a>
                    </li>
                    <li><a href="gallery.php" class="nav-link <?php echo ($currentPage == 'gallery.php') ? 'text-orange-400 border-b-2 border-orange-400' : ''; ?> transition duration-300">Gallery</a></li>
                    <li><a href="contact.php" class="nav-link <?php echo ($currentPage == 'contact.php') ? 'text-orange-400 border-b-2 border-orange-400' : ''; ?> transition duration-300">Contact</a></li>
                    <li><a href="request-quote.php" class="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-4 py-2 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-lg <?php echo ($currentPage == 'request-quote.php') ? 'ring-2 ring-white ring-opacity-50' : ''; ?>">Request Quote</a></li>
                </ul>

                <button id="mobile-menu-toggle" class="lg:hidden focus:outline-none transition duration-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Mega Menu -->
                <div class="mega-menu" id="tours-mega-menu">
                        <div class="mega-menu-content">
                            <!-- Tours Section -->
                            <div class="mega-menu-section">
                                <a href="tours.php#packages" class="mega-menu-title">
                                    <i class="fas fa-map-marked-alt"></i>
                                    Tour Packages
                                </a>
                                <div class="mega-menu-grid">
                                    <?php if (empty($headerPackageTypes)): ?>
                                        <div class="mega-menu-column">
                                            <h4 class="mega-menu-column-title">
                                                <i class="fas fa-info-circle"></i>
                                                No Package Types Available
                                            </h4>
                                            <ul class="mega-menu-items">
                                                <li><span style="color: rgba(255, 255, 255, 0.6); font-style: italic;">Package types will appear here once added</span></li>
                                            </ul>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($headerPackageTypes as $type): ?>
                                            <div class="mega-menu-column">
                                                <a href="tours.php?package_type=<?php echo $type['package_type_id']; ?>" class="mega-menu-column-title">
                                                    <i class="fas fa-tag"></i>
                                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                                </a>
                                                <ul class="mega-menu-items">
                                                    <?php
                                                    $typePackages = $headerPackagesByType[$type['package_type_id']] ?? [];
                                                    $headerDisplayPackages = array_slice($typePackages, 0, 6); // Limit to 6 packages per type
                                                    ?>
                                                    <?php if (empty($headerDisplayPackages)): ?>
                                                        <li><span style="color: rgba(255, 255, 255, 0.6); font-style: italic;">No packages available</span></li>
                                                    <?php else: ?>
                                                        <?php foreach ($headerDisplayPackages as $megaPackage): ?>
                                                            <li>
                                                                <a href="package-details.php?id=<?php echo $megaPackage['tour_package_id']; ?>&from=tours">
                                                                    <?php
                                                                    // Smart formatting based on package type and duration (same as index.php)
                                                                    $packageName = $megaPackage['name'];
                                                                    $tourType = $megaPackage['type_name'] ?: 'Tour';
                                                                    $duration = $megaPackage['duration'];

                                                                    // Check if tour type already implies duration
                                                                    $lowerTourType = strtolower($tourType);
                                                                    $typeImpliesDuration = strpos($lowerTourType, 'day') !== false ||
                                                                                        strpos($lowerTourType, 'overnight') !== false ||
                                                                                        strpos($lowerTourType, 'night') !== false;

                                                                    if ($typeImpliesDuration) {
                                                                        // Format: Tour Type + Package Name
                                                                        $formattedName = "$tourType $packageName";
                                                                    } elseif (!empty($duration)) {
                                                                        // Format: Duration + Package Name + Tour Type
                                                                        $formattedName = "$duration $packageName $tourType";
                                                                    } else {
                                                                        // Format: Package Name + Tour Type
                                                                        $formattedName = "$packageName $tourType";
                                                                    }

                                                                    echo htmlspecialchars($formattedName);
                                                                    ?>
                                                                </a>
                                                            </li>
                                                        <?php endforeach; ?>
                                                        <?php if (count($typePackages) > 6): ?>
                                                            <li>
                                                                <a href="tours.php?package_type=<?php echo $type['package_type_id']; ?>" style="color: #fef3c7; font-weight: 600; background-color: rgba(255, 255, 255, 0.1); border-radius: 4px; padding: 0.25rem 0.5rem; display: inline-block; margin-top: 0.25rem;">
                                                                    View all <?php echo count($typePackages); ?> packages →
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Destinations Section -->
                            <div class="mega-menu-section">
                                <a href="tours.php#destinations" class="mega-menu-title">
                                    <i class="fas fa-globe-africa"></i>
                                    Our Destinations
                                </a>
                                <div class="destinations-list">
                                    <?php if (empty($headerDestinations)): ?>
                                        <div class="destinations-column">
                                            <ul class="destinations-items">
                                                <li><span style="color: rgba(255, 255, 255, 0.6); font-style: italic;">Destinations will appear here once added</span></li>
                                            </ul>
                                        </div>
                                    <?php else: ?>
                                        <?php
                                        // Split destinations into 4 columns with 2 destinations each
                                        $headerDisplayDestinations = array_slice($headerDestinations, 0, 8); // Limit to 8 destinations
                                        $destinationChunks = array_chunk($headerDisplayDestinations, 2); // 2 destinations per column
                                        ?>
                                        <?php foreach ($destinationChunks as $chunkIndex => $destinationChunk): ?>
                                            <div class="destinations-column">
                                                <ul class="destinations-items">
                                                    <?php foreach ($destinationChunk as $headerDestination): ?>
                                                        <li>
                                                            <a href="destination-details.php?id=<?php echo $headerDestination['destination_id']; ?>&from=tours">
                                                                <?php echo htmlspecialchars($headerDestination['name']); ?>
                                                            </a>
                                                        </li>
                                                    <?php endforeach; ?>
                                                    <?php if ($chunkIndex === count($destinationChunks) - 1 && count($headerDestinations) > 8): ?>
                                                        <li>
                                                            <a href="tours.php" style="color: #fef3c7; font-weight: 600; background-color: rgba(255, 255, 255, 0.1); border-radius: 4px; padding: 0.25rem 0.5rem; display: inline-block; margin-top: 0.25rem;">
                                                                View all <?php echo count($headerDestinations); ?> destinations →
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</nav>

<!-- Mobile Navigation Menu -->
<div id="mobile-menu" class="fixed inset-0 bg-black bg-opacity-50 z-50 opacity-0 invisible">
    <div class="mobile-menu-panel fixed top-0 left-0 w-full bg-white shadow-lg transform -translate-y-full" style="height: fit-content; max-height: 100vh; overflow-y: auto;">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Menu</h3>
                <button id="mobile-menu-close" class="text-gray-600 hover:text-orange-500 transition duration-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <nav>
                <ul class="space-y-4">
                    <li><a href="index.php" class="block <?php echo ($currentPage == 'index.php') ? 'text-orange-500 font-semibold' : 'text-gray-800 hover:text-orange-500'; ?> transition duration-300">Home</a></li>
                    <li><a href="about.php" class="block <?php echo ($currentPage == 'about.php') ? 'text-orange-500 font-semibold' : 'text-gray-800 hover:text-orange-500'; ?> transition duration-300">About Us</a></li>
                    <li>
                        <button id="mobile-tours-toggle" class="w-full text-left flex items-center justify-between <?php echo ($currentPage == 'tours.php') ? 'text-orange-500 font-semibold' : 'text-gray-800 hover:text-orange-500'; ?> transition duration-300">
                            Tours & Destinations
                            <i class="fas fa-chevron-down transition-transform duration-300" id="mobile-tours-chevron"></i>
                        </button>
                        <div id="mobile-tours-submenu" class="mt-2 ml-4 space-y-3 max-h-0 overflow-hidden transition-all duration-300">
                            <a href="tours.php" class="block text-sm text-gray-600 hover:text-orange-500 transition duration-300 font-medium">All Tours & Destinations</a>

                            <!-- Tour Package Types Section -->
                            <div class="border-t border-gray-200 pt-3">
                                <span class="block text-xs text-gray-500 font-semibold mb-2 uppercase tracking-wide">Tour Package Types</span>
                                <?php if (empty($headerPackageTypes)): ?>
                                    <span class="block text-xs text-gray-400 italic">No package types available</span>
                                <?php else: ?>
                                    <?php foreach ($headerPackageTypes as $type): ?>
                                        <div class="mb-3">
                                            <button class="mobile-package-type-toggle w-full text-left flex items-center justify-between text-sm text-gray-700 hover:text-orange-500 font-medium transition duration-300" data-type-id="<?php echo $type['package_type_id']; ?>">
                                                <span class="flex items-center">
                                                    <i class="fas fa-tag text-orange-400 mr-2 text-xs"></i>
                                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                                </span>
                                                <i class="fas fa-chevron-down text-xs transition-transform duration-300 mobile-package-chevron"></i>
                                            </button>
                                            <div class="mobile-package-submenu ml-6 mt-1 space-y-1 max-h-0 overflow-hidden transition-all duration-300">
                                                <?php
                                                $typePackages = $headerPackagesByType[$type['package_type_id']] ?? [];
                                                $mobileDisplayPackages = array_slice($typePackages, 0, 4); // Limit to 4 packages per type for mobile
                                                ?>
                                                <?php if (empty($mobileDisplayPackages)): ?>
                                                    <span class="block text-xs text-gray-400 italic py-1">No packages available</span>
                                                <?php else: ?>
                                                    <?php foreach ($mobileDisplayPackages as $mobilePackage): ?>
                                                        <a href="package-details.php?id=<?php echo $mobilePackage['tour_package_id']; ?>&from=tours" class="block text-xs text-gray-600 hover:text-orange-500 transition duration-300 py-1 leading-relaxed">
                                                            <?php
                                                            // Smart formatting based on package type and duration (same as desktop mega menu)
                                                            $packageName = $mobilePackage['name'];
                                                            $tourType = $mobilePackage['type_name'] ?: 'Tour';
                                                            $duration = $mobilePackage['duration'];

                                                            // Check if tour type already implies duration
                                                            $lowerTourType = strtolower($tourType);
                                                            $typeImpliesDuration = strpos($lowerTourType, 'day') !== false ||
                                                                                strpos($lowerTourType, 'overnight') !== false ||
                                                                                strpos($lowerTourType, 'night') !== false;

                                                            if ($typeImpliesDuration) {
                                                                // Format: Tour Type + Package Name
                                                                $formattedName = "$tourType $packageName";
                                                            } elseif (!empty($duration)) {
                                                                // Format: Duration + Package Name + Tour Type
                                                                $formattedName = "$duration $packageName $tourType";
                                                            } else {
                                                                // Format: Package Name + Tour Type
                                                                $formattedName = "$packageName $tourType";
                                                            }

                                                            echo htmlspecialchars($formattedName);
                                                            ?>
                                                        </a>
                                                    <?php endforeach; ?>
                                                    <?php if (count($typePackages) > 4): ?>
                                                        <a href="tours.php?package_type=<?php echo $type['package_type_id']; ?>" class="block text-xs text-orange-500 hover:text-orange-600 font-medium transition duration-300 py-1">
                                                            View all <?php echo count($typePackages); ?> packages →
                                                        </a>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>

                            <!-- Destinations Section -->
                            <div class="border-t border-gray-200 pt-3">
                                <span class="block text-xs text-gray-500 font-semibold mb-2 uppercase tracking-wide">Destinations</span>
                                <?php if (empty($headerDestinations)): ?>
                                    <span class="block text-xs text-gray-400 italic">No destinations available</span>
                                <?php else: ?>
                                    <?php
                                    $mobileDestinations = array_slice($headerDestinations, 0, 6); // Show up to 6 destinations on mobile
                                    foreach ($mobileDestinations as $headerDestination):
                                    ?>
                                        <a href="destination-details.php?id=<?php echo $headerDestination['destination_id']; ?>&from=tours" class="block text-sm text-gray-600 hover:text-orange-500 transition duration-300 py-1 flex items-center">
                                            <i class="fas fa-map-marker-alt text-orange-400 mr-2 text-xs"></i>
                                            <?php echo htmlspecialchars($headerDestination['name']); ?>
                                        </a>
                                    <?php endforeach; ?>
                                    <?php if (count($headerDestinations) > 6): ?>
                                        <a href="tours.php#destinations" class="block text-sm text-orange-500 hover:text-orange-600 font-medium transition duration-300 py-1 mt-2">
                                            View all <?php echo count($headerDestinations); ?> destinations →
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </li>
                    <li><a href="gallery.php" class="block <?php echo ($currentPage == 'gallery.php') ? 'text-orange-500 font-semibold' : 'text-gray-800 hover:text-orange-500'; ?> transition duration-300">Gallery</a></li>
                    <li><a href="contact.php" class="block <?php echo ($currentPage == 'contact.php') ? 'text-orange-500 font-semibold' : 'text-gray-800 hover:text-orange-500'; ?> transition duration-300">Contact</a></li>
                    <li class="pt-4 border-t border-gray-200">
                        <a href="request-quote.php" class="block w-full text-center bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-4 py-3 rounded-lg font-semibold transition duration-300 transform hover:scale-105 shadow-lg <?php echo ($currentPage == 'request-quote.php') ? 'ring-2 ring-orange-300' : ''; ?>">
                            <i class="fas fa-paper-plane mr-2"></i>Request Quote
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<script>
    // Navbar functionality - self-contained
    (function() {
        let lastScrollTop = 0;
        let isNavVisible = true;
        const scrollThreshold = 100;
        const scrollDelta = 5;

        const mainNav = document.getElementById('main-nav');
        const hasHeroSection = mainNav.getAttribute('data-has-hero') === 'true';

        // Mobile menu elements
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuClose = document.getElementById('mobile-menu-close');

        // Handle scroll behavior
        function handleScroll() {
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollingDown = currentScrollTop > lastScrollTop;
            const scrollDeltaAmount = Math.abs(currentScrollTop - lastScrollTop);

            // Handle transparency/solid state
            if (hasHeroSection) {
                if (currentScrollTop > scrollThreshold) {
                    mainNav.classList.add('scrolled');
                } else {
                    mainNav.classList.remove('scrolled');
                }
            }

            // Handle show/hide logic
            if (currentScrollTop <= 0) {
                // Always show at very top
                showNavigation();
            } else if (currentScrollTop <= scrollThreshold) {
                // Show when near top
                showNavigation();
            } else if (scrollDeltaAmount > scrollDelta) {
                if (scrollingDown && currentScrollTop > 100) {
                    // Hide when scrolling down
                    hideNavigation();
                } else if (!scrollingDown) {
                    // Show when scrolling up
                    showNavigation();
                }
            }

            lastScrollTop = currentScrollTop;

            // Update mega menu position if it exists
            if (window.updateMegaMenuPosition) {
                window.updateMegaMenuPosition();
            }
        }

        function showNavigation() {
            if (!isNavVisible) {
                mainNav.classList.remove('nav-hidden');
                mainNav.classList.add('nav-visible');
                isNavVisible = true;
            }
        }

        function hideNavigation() {
            if (isNavVisible) {
                mainNav.classList.remove('nav-visible');
                mainNav.classList.add('nav-hidden');
                isNavVisible = false;
            }
        }

        // Mobile menu functions
        function openMobileMenu() {
            mobileMenu.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenu() {
            mobileMenu.classList.remove('show');
            document.body.style.overflow = '';
            // Reset mobile tours submenu when closing
            const mobileToursSubmenu = document.getElementById('mobile-tours-submenu');
            const mobileToursChevron = document.getElementById('mobile-tours-chevron');
            if (mobileToursSubmenu && mobileToursChevron) {
                mobileToursSubmenu.style.maxHeight = '0';
                mobileToursChevron.style.transform = 'rotate(0deg)';
            }

            // Reset all package type submenus
            const packageSubmenus = document.querySelectorAll('.mobile-package-submenu');
            const packageChevrons = document.querySelectorAll('.mobile-package-chevron');
            packageSubmenus.forEach(submenu => submenu.style.maxHeight = '0');
            packageChevrons.forEach(chevron => chevron.style.transform = 'rotate(0deg)');
        }

        // Initialize mobile tours dropdown
        function initializeMobileToursDropdown() {
            const mobileToursToggle = document.getElementById('mobile-tours-toggle');
            const mobileToursSubmenu = document.getElementById('mobile-tours-submenu');
            const mobileToursChevron = document.getElementById('mobile-tours-chevron');

            if (!mobileToursToggle || !mobileToursSubmenu || !mobileToursChevron) return;

            // Main tours dropdown toggle
            mobileToursToggle.addEventListener('click', function() {
                const isOpen = mobileToursSubmenu.style.maxHeight && mobileToursSubmenu.style.maxHeight !== '0px';

                if (isOpen) {
                    // Close main menu and all package submenus
                    mobileToursSubmenu.style.maxHeight = '0';
                    mobileToursChevron.style.transform = 'rotate(0deg)';

                    // Close all package type submenus
                    const packageSubmenus = document.querySelectorAll('.mobile-package-submenu');
                    const packageChevrons = document.querySelectorAll('.mobile-package-chevron');
                    packageSubmenus.forEach(submenu => submenu.style.maxHeight = '0');
                    packageChevrons.forEach(chevron => chevron.style.transform = 'rotate(0deg)');
                } else {
                    mobileToursSubmenu.style.maxHeight = mobileToursSubmenu.scrollHeight + 'px';
                    mobileToursChevron.style.transform = 'rotate(180deg)';
                }
            });

            // Initialize package type toggles
            const packageTypeToggles = document.querySelectorAll('.mobile-package-type-toggle');
            packageTypeToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const submenu = this.nextElementSibling;
                    const chevron = this.querySelector('.mobile-package-chevron');

                    if (!submenu || !chevron) return;

                    const isOpen = submenu.style.maxHeight && submenu.style.maxHeight !== '0px';

                    if (isOpen) {
                        submenu.style.maxHeight = '0';
                        chevron.style.transform = 'rotate(0deg)';
                    } else {
                        // Close other package submenus first
                        packageTypeToggles.forEach(otherToggle => {
                            if (otherToggle !== this) {
                                const otherSubmenu = otherToggle.nextElementSibling;
                                const otherChevron = otherToggle.querySelector('.mobile-package-chevron');
                                if (otherSubmenu) otherSubmenu.style.maxHeight = '0';
                                if (otherChevron) otherChevron.style.transform = 'rotate(0deg)';
                            }
                        });

                        // Open this submenu
                        submenu.style.maxHeight = submenu.scrollHeight + 'px';
                        chevron.style.transform = 'rotate(180deg)';

                        // Update main menu height to accommodate expanded submenu
                        setTimeout(() => {
                            mobileToursSubmenu.style.maxHeight = mobileToursSubmenu.scrollHeight + 'px';
                        }, 100);
                    }
                });
            });
        }

        // Event listeners
        let ticking = false;
        window.addEventListener('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        });

        // Mobile menu event listeners
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', openMobileMenu);
        }

        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', closeMobileMenu);
        }

        // Close mobile menu when clicking overlay
        if (mobileMenu) {
            mobileMenu.addEventListener('click', function(e) {
                if (e.target === mobileMenu) {
                    closeMobileMenu();
                }
            });
        }

        // Close mobile menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });

        // Add body class based on hero section presence
        document.body.classList.add(hasHeroSection ? 'has-hero-section' : 'no-hero-section');

        // Create and manage go-to-top button
        createGoToTopButton();

        // Initialize footer year
        initializeFooter();

        // Initialize mega menu functionality
        initializeMegaMenu();

        // Initialize mobile tours dropdown
        initializeMobileToursDropdown();

        function initializeMegaMenu() {
            const toursNavLink = document.getElementById('tours-nav-link');
            const toursMegaMenu = document.getElementById('tours-mega-menu');
            const toursChevron = document.getElementById('tours-chevron');
            let megaMenuTimeout;

            if (!toursNavLink || !toursMegaMenu) {
                console.log('Mega menu elements not found:', {
                    toursNavLink: !!toursNavLink,
                    toursMegaMenu: !!toursMegaMenu
                });
                return;
            }

            console.log('Mega menu initialized successfully');

            // Function to update mega menu position based on navbar state
            function updateMegaMenuPosition() {
                const navbarHeight = mainNav.offsetHeight;
                toursMegaMenu.style.top = navbarHeight + 'px';

                // Add appropriate classes based on navbar state
                if (mainNav.classList.contains('scrolled')) {
                    toursMegaMenu.classList.add('navbar-scrolled');
                    toursMegaMenu.classList.remove('navbar-transparent');
                } else {
                    toursMegaMenu.classList.add('navbar-transparent');
                    toursMegaMenu.classList.remove('navbar-scrolled');
                }
            }

            // Make updateMegaMenuPosition globally accessible
            window.updateMegaMenuPosition = updateMegaMenuPosition;

            // Show mega menu on hover
            toursNavLink.addEventListener('mouseenter', function() {
                clearTimeout(megaMenuTimeout);
                updateMegaMenuPosition(); // Update position before showing
                toursMegaMenu.classList.add('show');
                toursChevron.style.transform = 'rotate(180deg)';
            });

            // Hide mega menu when leaving the nav item and menu area
            toursNavLink.addEventListener('mouseleave', function() {
                megaMenuTimeout = setTimeout(function() {
                    toursMegaMenu.classList.remove('show');
                    toursChevron.style.transform = 'rotate(0deg)';
                }, 800);
            });

            // Keep mega menu open when hovering over it
            toursMegaMenu.addEventListener('mouseenter', function() {
                clearTimeout(megaMenuTimeout);
            });

            // Hide mega menu when leaving the menu area
            toursMegaMenu.addEventListener('mouseleave', function() {
                toursMegaMenu.classList.remove('show');
                toursChevron.style.transform = 'rotate(0deg)';
            });

            // Close mega menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    toursMegaMenu.classList.remove('show');
                    toursChevron.style.transform = 'rotate(0deg)';
                }
            });

            // Close mega menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!toursNavLink.contains(e.target) && !toursMegaMenu.contains(e.target)) {
                    toursMegaMenu.classList.remove('show');
                    toursChevron.style.transform = 'rotate(0deg)';
                }
            });

            // Initial position update
            updateMegaMenuPosition();
        }

        function createGoToTopButton() {
            const goToTopBtn = document.createElement('button');
            goToTopBtn.id = 'go-to-top';
            goToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
            goToTopBtn.className = 'fixed bottom-6 right-6 w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 z-40 opacity-0 invisible';
            goToTopBtn.setAttribute('aria-label', 'Go to top');
            goToTopBtn.title = 'Go to top';

            goToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            document.body.appendChild(goToTopBtn);

            // Show/hide go-to-top button based on scroll
            function handleGoToTopButton(scrollTop) {
                if (scrollTop > 300) {
                    goToTopBtn.style.opacity = '1';
                    goToTopBtn.style.visibility = 'visible';
                } else {
                    goToTopBtn.style.opacity = '0';
                    goToTopBtn.style.visibility = 'hidden';
                }
            }

            // Add go-to-top button handling to scroll event
            const originalHandleScroll = handleScroll;
            handleScroll = function() {
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                originalHandleScroll();
                handleGoToTopButton(currentScrollTop);
            };
        }

        function initializeFooter() {
            const yearElement = document.getElementById('current-year');
            if (yearElement) {
                yearElement.textContent = new Date().getFullYear();
            }
        }

    })();
</script>