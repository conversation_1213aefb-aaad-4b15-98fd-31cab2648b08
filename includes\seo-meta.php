<?php
/**
 * SEO Meta Tags Helper for Meleva Tours and Travel
 * Provides consistent meta tags, Open Graph, and Twitter Card data across all pages
 */

class SEOMeta {
    private $baseUrl;
    private $siteName = 'Meleva Tours & Travel';
    private $defaultDescription = 'Experience authentic African safari adventures with Meleva Tours & Travel. Discover Kenya\'s wildlife, stunning landscapes, and cultural heritage through our expertly crafted tour packages.';
    private $defaultKeywords = 'Kenya safari, African tours, wildlife safari, Maasai Mara, Kilifi tours, Kenya travel, safari packages, African adventure, wildlife photography, cultural tours';
    private $twitterHandle = '@MelevaTours';
    
    public function __construct() {
        $this->baseUrl = $this->getBaseUrl();
    }
    
    public function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            return $protocol . '://' . $host . '/meleva';
        } else {
            return $protocol . '://' . $host;
        }
    }
    
    public function generateMetaTags($options = []) {
        // Check if this page should not be indexed
        $noIndex = $options['noIndex'] ?? false;

        if ($noIndex) {
            // For pages that should not be indexed (payment pages, etc.)
            $title = $options['title'] ?? 'Secure Page';
            echo "<!-- Private Page Meta Tags -->\n";
            echo "<title>" . htmlspecialchars($title . ' | ' . $this->siteName) . "</title>\n";
            echo "<meta name=\"robots\" content=\"noindex, nofollow\">\n";
            echo "<meta name=\"googlebot\" content=\"noindex, nofollow\">\n";
            return;
        }

        // Default values for public pages
        $title = $options['title'] ?? 'Authentic African Safari Adventures';
        $description = $options['description'] ?? $this->defaultDescription;
        $keywords = $options['keywords'] ?? $this->defaultKeywords;
        $image = $options['image'] ?? $this->baseUrl . '/images/hero-bg.jpg';
        $url = $options['url'] ?? $this->baseUrl . $_SERVER['REQUEST_URI'];
        $type = $options['type'] ?? 'website';
        $author = $options['author'] ?? $this->siteName;

        // Clean URL (remove query parameters for canonical)
        $canonicalUrl = strtok($url, '?');

        // Generate full title - check if title already includes site name
        if (strpos($title, 'Meleva Tours') !== false) {
            $fullTitle = $title; // Title already includes site name
        } else {
            $fullTitle = $title . ' | ' . $this->siteName;
            if (strlen($fullTitle) > 60) {
                $fullTitle = $title . ' | Meleva Tours';
            }
        }

        echo "<!-- SEO Meta Tags -->\n";
        echo "<title>" . htmlspecialchars($fullTitle) . "</title>\n";
        echo "<meta name=\"description\" content=\"" . htmlspecialchars($description) . "\">\n";
        echo "<meta name=\"keywords\" content=\"" . htmlspecialchars($keywords) . "\">\n";
        echo "<meta name=\"author\" content=\"" . htmlspecialchars($author) . "\">\n";
        echo "<meta name=\"robots\" content=\"index, follow\">\n";
        echo "<link rel=\"canonical\" href=\"" . htmlspecialchars($canonicalUrl) . "\">\n";
        
        // Open Graph Meta Tags
        echo "\n<!-- Open Graph Meta Tags -->\n";
        echo "<meta property=\"og:title\" content=\"" . htmlspecialchars($title) . "\">\n";
        echo "<meta property=\"og:description\" content=\"" . htmlspecialchars($description) . "\">\n";
        echo "<meta property=\"og:image\" content=\"" . htmlspecialchars($image) . "\">\n";
        echo "<meta property=\"og:url\" content=\"" . htmlspecialchars($url) . "\">\n";
        echo "<meta property=\"og:type\" content=\"" . htmlspecialchars($type) . "\">\n";
        echo "<meta property=\"og:site_name\" content=\"" . htmlspecialchars($this->siteName) . "\">\n";
        echo "<meta property=\"og:locale\" content=\"en_US\">\n";
        
        // Twitter Card Meta Tags
        echo "\n<!-- Twitter Card Meta Tags -->\n";
        echo "<meta name=\"twitter:card\" content=\"summary_large_image\">\n";
        echo "<meta name=\"twitter:site\" content=\"" . htmlspecialchars($this->twitterHandle) . "\">\n";
        echo "<meta name=\"twitter:title\" content=\"" . htmlspecialchars($title) . "\">\n";
        echo "<meta name=\"twitter:description\" content=\"" . htmlspecialchars($description) . "\">\n";
        echo "<meta name=\"twitter:image\" content=\"" . htmlspecialchars($image) . "\">\n";
        
        // Additional SEO tags
        echo "\n<!-- Additional SEO Tags -->\n";
        echo "<meta name=\"theme-color\" content=\"#f97316\">\n";
        echo "<meta name=\"msapplication-TileColor\" content=\"#f97316\">\n";
        
        // Geo tags for local SEO
        echo "<meta name=\"geo.region\" content=\"KE-03\">\n";
        echo "<meta name=\"geo.placename\" content=\"Kilifi, Kenya\">\n";
        echo "<meta name=\"geo.position\" content=\"-3.6309,39.8493\">\n";
        echo "<meta name=\"ICBM\" content=\"-3.6309,39.8493\">\n";
    }
    
    public function generateStructuredData($options = []) {
        $type = $options['type'] ?? 'Organization';
        
        switch ($type) {
            case 'Organization':
                return $this->generateOrganizationSchema();
            case 'TourPackage':
                return $this->generateTourPackageSchema($options);
            case 'Destination':
                return $this->generateDestinationSchema($options);
            case 'Review':
                return $this->generateReviewSchema($options);
            default:
                return $this->generateOrganizationSchema();
        }
    }
    
    private function generateOrganizationSchema() {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "TravelAgency",
            "name" => $this->siteName,
            "description" => $this->defaultDescription,
            "url" => $this->baseUrl,
            "logo" => $this->baseUrl . "/images/meleva-lg.png",
            "image" => $this->baseUrl . "/images/hero-bg.jpg",
            "telephone" => "+254-XXX-XXXXXX", // Update with actual phone
            "email" => "<EMAIL>",
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => "Mavueni",
                "addressLocality" => "Kilifi",
                "addressRegion" => "Kilifi County",
                "addressCountry" => "KE"
            ],
            "geo" => [
                "@type" => "GeoCoordinates",
                "latitude" => -3.6309,
                "longitude" => 39.8493
            ],
            "sameAs" => [
                "https://www.facebook.com/melevatours",
                "https://www.instagram.com/melevatours",
                "https://www.twitter.com/melevatours"
            ],
            "areaServed" => [
                "@type" => "Country",
                "name" => "Kenya"
            ],
            "serviceType" => [
                "Safari Tours",
                "Wildlife Photography Tours",
                "Cultural Tours",
                "Adventure Travel",
                "Eco Tourism"
            ]
        ];
        
        return json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
    
    private function generateTourPackageSchema($options) {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "TouristTrip",
            "name" => $options['name'] ?? '',
            "description" => $options['description'] ?? '',
            "image" => $options['image'] ?? '',
            "url" => $options['url'] ?? '',
            "provider" => [
                "@type" => "TravelAgency",
                "name" => $this->siteName,
                "url" => $this->baseUrl
            ],
            "offers" => [
                "@type" => "Offer",
                "price" => $options['price'] ?? '',
                "priceCurrency" => "USD",
                "availability" => "https://schema.org/InStock"
            ]
        ];
        
        if (isset($options['duration'])) {
            $schema['duration'] = $options['duration'];
        }
        
        if (isset($options['location'])) {
            $schema['touristType'] = "Safari Enthusiast";
            $schema['itinerary'] = [
                "@type" => "TouristDestination",
                "name" => $options['location']
            ];
        }
        
        return json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
    
    private function generateDestinationSchema($options) {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "TouristDestination",
            "name" => $options['name'] ?? '',
            "description" => $options['description'] ?? '',
            "image" => $options['image'] ?? '',
            "url" => $options['url'] ?? '',
            "geo" => [
                "@type" => "GeoCoordinates",
                "latitude" => $options['latitude'] ?? '',
                "longitude" => $options['longitude'] ?? ''
            ],
            "address" => [
                "@type" => "PostalAddress",
                "addressCountry" => "KE"
            ]
        ];
        
        return json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
    
    private function generateReviewSchema($options) {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "Review",
            "reviewBody" => $options['review'] ?? '',
            "reviewRating" => [
                "@type" => "Rating",
                "ratingValue" => $options['rating'] ?? 5,
                "bestRating" => 5
            ],
            "author" => [
                "@type" => "Person",
                "name" => $options['author'] ?? ''
            ],
            "itemReviewed" => [
                "@type" => "TravelAgency",
                "name" => $this->siteName
            ]
        ];
        
        return json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
}
