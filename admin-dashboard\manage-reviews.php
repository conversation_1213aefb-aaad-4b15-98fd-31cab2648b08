<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';

// Require authentication
Auth::requireLogin();

$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
try {
    $reportModel = new Report();
    $stats = $reportModel->generateDashboardStats();
} catch (Exception $e) {
    // Fallback stats if dashboard stats fail
    $stats = [
        'destinations' => 0,
        'tour_packages' => 0,
        'messages' => 0,
        'unread_messages' => 0,
        'total_quotes' => 0,
        'pending_quotes' => 0,
        'quoted_quotes' => 0,
        'accepted_quotes' => 0,
        'total_bookings' => 0,
        'active_bookings' => 0,
        'total_booking_value' => 0,
        'total_payments' => 0,
        'pending_payments' => 0,
        'total_revenue' => 0,
        'recent_activity' => []
    ];
    error_log("Dashboard stats error in manage-reviews.php: " . $e->getMessage());
}

// Handle form submissions
$success = '';
$error = '';

// Debug: Log all POST data
if ($_POST) {
    error_log("POST data received: " . print_r($_POST, true));
}

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_settings':
                $result = updateReviewSettings($_POST);
                if ($result) {
                    $success = 'Review settings updated successfully!';
                } else {
                    $error = 'Failed to update review settings.';
                }
                break;
            case 'update_reviews':
                $result = updateManualReviews($_POST);
                if ($result) {
                    $success = 'Manual reviews updated successfully!';
                } else {
                    $error = 'Failed to update manual reviews.';
                }
                break;
        }
    }
}

function updateReviewSettings($data) {
    try {
        $config_file = '../config/google-config.php';
        
        if (!file_exists($config_file)) {
            return false;
        }
        
        $config_content = file_get_contents($config_file);
        
        // Update settings based on radio button selection
        $use_manual = ($data['review_mode'] === 'manual') ? 'true' : 'false';
        $use_mixed = ($data['review_mode'] === 'mixed') ? 'true' : 'false';
        
        $config_content = preg_replace(
            "/define\('USE_MANUAL_REVIEWS', (true|false)\);/",
            "define('USE_MANUAL_REVIEWS', $use_manual);",
            $config_content
        );
        
        $config_content = preg_replace(
            "/define\('USE_MIXED_REVIEWS', (true|false)\);/",
            "define('USE_MIXED_REVIEWS', $use_mixed);",
            $config_content
        );
        
        return file_put_contents($config_file, $config_content) !== false;
    } catch (Exception $e) {
        error_log("Error updating review settings: " . $e->getMessage());
        return false;
    }
}

function updateManualReviews($data) {
    try {
        $reviews = [];

        // Debug: Log the incoming data
        error_log("updateManualReviews called with data: " . print_r($data, true));

        for ($i = 0; $i < 5; $i++) {
            if (!empty($data["review_name_$i"])) {
                $reviews[] = [
                    'name' => trim(strip_tags($data["review_name_$i"])),
                    'rating' => (int)$data["review_rating_$i"],
                    'text' => trim(strip_tags($data["review_text_$i"])),
                    'date' => trim(strip_tags($data["review_date_$i"])),
                    'avatar' => null,
                    'source' => 'Google',
                    'location' => trim(strip_tags($data["review_location_$i"] ?? ''))
                ];
            }
        }

        // Debug: Log the processed reviews
        error_log("Processed reviews: " . print_r($reviews, true));

        // Update config file
        $config_file = '../config/google-config.php';
        if (!file_exists($config_file)) {
            error_log("Config file not found: " . $config_file);
            return false;
        }

        $config_content = file_get_contents($config_file);
        if ($config_content === false) {
            error_log("Failed to read config file");
            return false;
        }

        $reviews_php = var_export($reviews, true);

        // Pattern to match both array formats: [...] and array(...)
        $pattern = '/\$manual_reviews = (?:\[.*?\]|array \(.*?\));/s';
        $replacement = '$manual_reviews = ' . $reviews_php . ';';

        // Debug: Check if pattern matches
        if (preg_match($pattern, $config_content)) {
            error_log("Pattern matched successfully");
        } else {
            error_log("Pattern did not match. Config content preview: " . substr($config_content, 0, 500));
        }

        $new_config_content = preg_replace($pattern, $replacement, $config_content);

        if ($new_config_content === null) {
            error_log("preg_replace failed");
            return false;
        }

        $result = file_put_contents($config_file, $new_config_content);
        if ($result === false) {
            error_log("Failed to write to config file");
            return false;
        }

        error_log("Successfully updated config file with " . count($reviews) . " reviews");
        return true;

    } catch (Exception $e) {
        error_log("Error updating manual reviews: " . $e->getMessage());
        return false;
    }
}

// Load current settings
$current_manual_reviews = [];
$use_manual_reviews = false;
$use_mixed_reviews = false;

try {
    if (file_exists('../config/google-config.php')) {
        require_once '../config/google-config.php';
        $current_manual_reviews = isset($manual_reviews) ? $manual_reviews : [];
        $use_manual_reviews = defined('USE_MANUAL_REVIEWS') && USE_MANUAL_REVIEWS;
        $use_mixed_reviews = defined('USE_MIXED_REVIEWS') && USE_MIXED_REVIEWS;
    }
} catch (Exception $e) {
    error_log("Error loading review settings: " . $e->getMessage());
}

// Determine current review mode
$current_mode = 'google'; // default
if ($use_manual_reviews) {
    $current_mode = 'manual';
} elseif ($use_mixed_reviews) {
    $current_mode = 'mixed';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Reviews - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="bg-gray-100">
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Manage Reviews</h1>
                    <p class="text-gray-600 mt-1">Configure review display settings and manage manual reviews</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.php" target="_blank" class="text-orange-500 hover:text-orange-600">
                        <i class="fas fa-external-link-alt mr-2"></i>View Website
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <main class="p-6">
            <!-- Success/Error Messages -->
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Review Mode Settings -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-cog mr-2 text-orange-500"></i>Review Display Settings
                </h2>
                <p class="text-gray-600 mb-6">Choose how reviews are displayed on your website.</p>
                
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <div class="space-y-4">
                        <label class="flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 <?php echo $current_mode === 'manual' ? 'border-orange-500 bg-orange-50' : 'border-gray-300'; ?>">
                            <input type="radio" name="review_mode" value="manual" 
                                   <?php echo $current_mode === 'manual' ? 'checked' : ''; ?>
                                   class="mt-1 mr-4 text-orange-500">
                            <div>
                                <span class="font-medium text-gray-900">Manual Reviews Only</span>
                                <p class="text-sm text-gray-600 mt-1">Display only the reviews you manually select below. Full control over content and display order.</p>
                            </div>
                        </label>
                        
                        <label class="flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 <?php echo $current_mode === 'mixed' ? 'border-orange-500 bg-orange-50' : 'border-gray-300'; ?>">
                            <input type="radio" name="review_mode" value="mixed"
                                   <?php echo $current_mode === 'mixed' ? 'checked' : ''; ?>
                                   class="mt-1 mr-4 text-orange-500">
                            <div>
                                <span class="font-medium text-gray-900">Mixed Reviews</span>
                                <p class="text-sm text-gray-600 mt-1">Combine manual reviews with Google API reviews for a diverse display.</p>
                            </div>
                        </label>
                        
                        <label class="flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 <?php echo $current_mode === 'google' ? 'border-orange-500 bg-orange-50' : 'border-gray-300'; ?>">
                            <input type="radio" name="review_mode" value="google"
                                   <?php echo $current_mode === 'google' ? 'checked' : ''; ?>
                                   class="mt-1 mr-4 text-orange-500">
                            <div>
                                <span class="font-medium text-gray-900">Google Reviews Only</span>
                                <p class="text-sm text-gray-600 mt-1">Automatically fetch and display Google Business Profile reviews via API.</p>
                            </div>
                        </label>
                    </div>
                    
                    <button type="submit" class="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        <i class="fas fa-save mr-2"></i>Update Settings
                    </button>
                </form>
            </div>

            <!-- Manual Reviews Editor -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-edit mr-2 text-orange-500"></i>Manual Reviews
                </h2>
                <p class="text-gray-600 mb-6">Edit the reviews that will be displayed when manual or mixed mode is selected. Leave fields empty to remove a review.</p>

                <form method="POST">
                    <input type="hidden" name="action" value="update_reviews">

                    <?php for ($i = 0; $i < 5; $i++):
                        $review = isset($current_manual_reviews[$i]) ? $current_manual_reviews[$i] : null;
                    ?>
                    <div class="border rounded-lg p-6 mb-6 <?php echo $review ? 'border-gray-300 bg-white' : 'border-gray-200 bg-gray-50'; ?>">
                        <h3 class="font-medium mb-4 flex items-center">
                            <i class="fas fa-star text-yellow-500 mr-2"></i>Review <?php echo $i + 1; ?>
                            <?php if ($review): ?>
                                <span class="ml-2 text-sm text-green-600 bg-green-100 px-2 py-1 rounded">Active</span>
                            <?php else: ?>
                                <span class="ml-2 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">Empty</span>
                            <?php endif; ?>
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-user mr-1"></i>Customer Name
                                </label>
                                <input type="text" name="review_name_<?php echo $i; ?>"
                                       value="<?php echo $review ? htmlspecialchars($review['name']) : ''; ?>"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="e.g., Sarah Johnson">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-star mr-1"></i>Rating
                                </label>
                                <select name="review_rating_<?php echo $i; ?>" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500">
                                    <option value="5" <?php echo ($review && $review['rating'] == 5) ? 'selected' : ''; ?>>⭐⭐⭐⭐⭐ (5 Stars)</option>
                                    <option value="4" <?php echo ($review && $review['rating'] == 4) ? 'selected' : ''; ?>>⭐⭐⭐⭐ (4 Stars)</option>
                                    <option value="3" <?php echo ($review && $review['rating'] == 3) ? 'selected' : ''; ?>>⭐⭐⭐ (3 Stars)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-calendar mr-1"></i>Date
                                </label>
                                <input type="text" name="review_date_<?php echo $i; ?>"
                                       value="<?php echo $review ? htmlspecialchars($review['date']) : ''; ?>"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="e.g., December 2024">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-map-marker-alt mr-1"></i>Location
                                </label>
                                <input type="text" name="review_location_<?php echo $i; ?>"
                                       value="<?php echo $review ? htmlspecialchars($review['location'] ?? '') : ''; ?>"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="e.g., Nairobi, Kenya">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-comment mr-1"></i>Review Text
                            </label>
                            <textarea name="review_text_<?php echo $i; ?>" rows="3"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                      placeholder="Enter the customer's review..."><?php echo $review ? htmlspecialchars($review['text']) : ''; ?></textarea>
                        </div>
                    </div>
                    <?php endfor; ?>

                    <div class="flex items-center justify-between">
                        <button type="submit" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                            <i class="fas fa-save mr-2"></i>Save Reviews
                        </button>
                        <p class="text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Changes will be visible on the website immediately after saving.
                        </p>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script>
        // Handle radio button changes for better UX
        document.querySelectorAll('input[name="review_mode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Update visual feedback
                document.querySelectorAll('label').forEach(label => {
                    if (label.querySelector('input[name="review_mode"]')) {
                        if (label.querySelector('input[name="review_mode"]').checked) {
                            label.classList.add('border-orange-500', 'bg-orange-50');
                            label.classList.remove('border-gray-300');
                        } else {
                            label.classList.remove('border-orange-500', 'bg-orange-50');
                            label.classList.add('border-gray-300');
                        }
                    }
                });
            });
        });

        // Auto-save functionality (optional)
        let saveTimeout;
        document.querySelectorAll('input, textarea, select').forEach(element => {
            if (element.name && element.name.startsWith('review_')) {
                element.addEventListener('input', function() {
                    clearTimeout(saveTimeout);
                    // Show unsaved changes indicator
                    const saveButton = document.querySelector('button[type="submit"]:last-of-type');
                    if (saveButton) {
                        saveButton.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Unsaved Changes';
                        saveButton.classList.remove('bg-green-500', 'hover:bg-green-600');
                        saveButton.classList.add('bg-yellow-500', 'hover:bg-yellow-600');
                    }
                });
            }
        });
    </script>
</body>
</html>
