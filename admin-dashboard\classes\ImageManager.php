<?php
/**
 * Enhanced Image Management Helper Class with Guaranteed 5MB Compression
 * Provides utilities for handling multiple image uploads with aggressive compression
 */

class ImageManager {
    private $uploadDir;
    private $category;
    private $allowedTypes;
    private $maxFileSize;
    private $maxWidth;
    private $maxHeight;
    private $minQuality;
    
    public function __construct($uploadDir = 'upload/images/destinations/', $category = 'destinations') {
        $this->uploadDir = $uploadDir;
        $this->category = $category;
        $this->allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB - GUARANTEED maximum
        $this->maxWidth = 1200;
        $this->maxHeight = 1200;
        $this->minQuality = 30; // Minimum quality before we start reducing dimensions

        // Ensure upload directory exists
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0777, true);
        }
    }
    
    /**
     * Process multiple file uploads for tour packages with guaranteed compression to 5MB max
     * @param array $files $_FILES array for multiple files
     * @param int $tourPackageId Tour Package ID to associate images with
     * @param int $userId User ID who uploaded the images
     * @param string $packageName Name for alt text generation
     * @param int $displayImageIndex Index of image to set as display (optional)
     * @return array Result with success status and details
     */
    public function processMultipleUploadsForPackages($files, $tourPackageId, $userId, $packageName, $displayImageIndex = null) {
        $result = [
            'success' => false,
            'uploaded_images' => [],
            'display_image_id' => null,
            'errors' => []
        ];

        if (empty($files['name'][0])) {
            $result['errors'][] = 'No files selected';
            return $result;
        }

        $uploadedImages = [];

        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                $result['errors'][] = "Upload error for file " . ($i + 1) . ": " . $this->getUploadErrorMessage($files['error'][$i]);
                continue;
            }

            $uploadResult = $this->processSingleUploadForPackage(
                $files['tmp_name'][$i],
                $files['name'][$i],
                $files['size'][$i],
                $tourPackageId,
                $userId,
                $packageName . ' image ' . ($i + 1)
            );

            if ($uploadResult['success']) {
                $uploadedImages[] = $uploadResult['image_id'];

                // Set as display image if specified
                if ($displayImageIndex !== null && $displayImageIndex == $i) {
                    $result['display_image_id'] = $uploadResult['image_id'];
                }
            } else {
                $result['errors'][] = $uploadResult['error'];
            }
        }

        // If no display image specified but images were uploaded, use first one
        if ($result['display_image_id'] === null && !empty($uploadedImages)) {
            $result['display_image_id'] = $uploadedImages[0];
        }

        // Update display image settings
        if ($result['display_image_id']) {
            $this->setDisplayImageForPackage($tourPackageId, $result['display_image_id']);
        }

        $result['success'] = !empty($uploadedImages);
        $result['uploaded_images'] = $uploadedImages;

        return $result;
    }

    /**
     * Process multiple file uploads with guaranteed compression to 5MB max
     * @param array $files $_FILES array for multiple files
     * @param int $destinationId Destination ID to associate images with
     * @param int $userId User ID who uploaded the images
     * @param string $destinationName Name for alt text generation
     * @param int $displayImageIndex Index of image to set as display (optional)
     * @return array Result with success status and details
     */
    public function processMultipleUploads($files, $destinationId, $userId, $destinationName, $displayImageIndex = null) {
        $result = [
            'success' => false,
            'uploaded_images' => [],
            'display_image_id' => null,
            'errors' => []
        ];
        
        if (empty($files['name'][0])) {
            $result['errors'][] = 'No files selected';
            return $result;
        }
        
        $imageModel = new Image();
        $uploadedImages = [];
        
        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                $result['errors'][] = "Upload error for file " . ($i + 1) . ": " . $this->getUploadErrorMessage($files['error'][$i]);
                continue;
            }
            
            $uploadResult = $this->processSingleUploadWithGuaranteedCompression(
                $files['tmp_name'][$i],
                $files['name'][$i],
                $files['size'][$i],
                $destinationId,
                $userId,
                $destinationName . ' image ' . ($i + 1)
            );
            
            if ($uploadResult['success']) {
                $uploadedImages[] = $uploadResult['image_id'];
                
                // Set as display image if specified
                if ($displayImageIndex !== null && $displayImageIndex == $i) {
                    $result['display_image_id'] = $uploadResult['image_id'];
                }
            } else {
                $result['errors'][] = $uploadResult['error'];
            }
        }
        
        // If no display image specified but images were uploaded, use first one
        if ($result['display_image_id'] === null && !empty($uploadedImages)) {
            $result['display_image_id'] = $uploadedImages[0];
        }
        
        // Update display image settings
        if ($result['display_image_id']) {
            $this->setDisplayImage($destinationId, $result['display_image_id']);
        }
        
        $result['success'] = !empty($uploadedImages);
        $result['uploaded_images'] = $uploadedImages;
        
        return $result;
    }

    /**
     * Process single file upload for tour package with guaranteed compression to max size
     * @param string $tmpName Temporary file path
     * @param string $fileName Original file name
     * @param int $fileSize File size in bytes
     * @param int $tourPackageId Tour Package ID
     * @param int $userId User ID
     * @param string $altText Alt text for the image
     * @return array Result with success status and image ID
     */
    private function processSingleUploadForPackage($tmpName, $fileName, $fileSize, $tourPackageId, $userId, $altText) {
        $result = ['success' => false, 'image_id' => null, 'error' => ''];

        // Validate file type
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedTypes)) {
            $result['error'] = "Invalid file type: " . $fileName . " (allowed: " . implode(', ', $this->allowedTypes) . ")";
            return $result;
        }

        // Generate unique filename
        $uniqueFileName = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $fileName);
        $targetPath = $this->uploadDir . $uniqueFileName;

        // Move uploaded file to temporary location
        if (!move_uploaded_file($tmpName, $targetPath)) {
            $result['error'] = "Failed to move uploaded file: " . $fileName;
            return $result;
        }

        // Compress image with guaranteed size limit
        $compressedPath = $this->compressImageToGuaranteedSize($targetPath, $this->maxFileSize);

        if (!$compressedPath) {
            // Clean up and return error
            if (file_exists($targetPath)) {
                unlink($targetPath);
            }
            $result['error'] = "Failed to compress image to required size: " . $fileName;
            return $result;
        }

        // Save to database
        $imageModel = new Image();
        $imageData = [
            'url' => $compressedPath,
            'alt_text' => $altText,
            'image_category' => 'tour_packages',
            'destination_id' => null,
            'tour_package_id' => $tourPackageId,
            'gallery_id' => null,
            'is_display_image' => 0,
            'uploaded_by_user_id' => $userId
        ];

        if ($imageModel->create($imageData)) {
            $result['success'] = true;
            $result['image_id'] = $imageModel->getLastInsertedId();
        } else {
            $result['error'] = "Failed to save image to database: " . $fileName;
            // Clean up uploaded file
            if (file_exists($compressedPath)) {
                unlink($compressedPath);
            }
        }

        return $result;
    }

    /**
     * Process single file upload with guaranteed compression to max size
     * @param string $tmpName Temporary file path
     * @param string $fileName Original file name
     * @param int $fileSize File size in bytes
     * @param int $destinationId Destination ID
     * @param int $userId User ID
     * @param string $altText Alt text for the image
     * @return array Result with success status and image ID
     */
    private function processSingleUploadWithGuaranteedCompression($tmpName, $fileName, $fileSize, $destinationId, $userId, $altText) {
        $result = ['success' => false, 'image_id' => null, 'error' => ''];
        
        // Validate file type
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedTypes)) {
            $result['error'] = "Invalid file type: " . $fileName . " (allowed: " . implode(', ', $this->allowedTypes) . ")";
            return $result;
        }
        
        // Generate unique filename
        $uniqueFileName = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $fileName);
        $targetPath = $this->uploadDir . $uniqueFileName;
        
        // Move uploaded file to temporary location
        if (!move_uploaded_file($tmpName, $targetPath)) {
            $result['error'] = "Failed to move uploaded file: " . $fileName;
            return $result;
        }
        
        // Compress image with guaranteed size limit
        $compressedPath = $this->compressImageToGuaranteedSize($targetPath, $this->maxFileSize);
        
        if (!$compressedPath) {
            // Clean up and return error
            if (file_exists($targetPath)) {
                unlink($targetPath);
            }
            $result['error'] = "Failed to compress image to required size: " . $fileName;
            return $result;
        }
        
        // Save to database
        $imageModel = new Image();
        $imageData = [
            'url' => $compressedPath,
            'alt_text' => $altText,
            'image_category' => 'destinations',
            'destination_id' => $destinationId,
            'tour_package_id' => null,
            'gallery_id' => null,
            'is_display_image' => 0,
            'uploaded_by_user_id' => $userId
        ];
        
        if ($imageModel->create($imageData)) {
            $result['success'] = true;
            $result['image_id'] = $imageModel->getLastInsertedId();
        } else {
            $result['error'] = "Failed to save image to database: " . $fileName;
            // Clean up uploaded file
            if (file_exists($compressedPath)) {
                unlink($compressedPath);
            }
        }
        
        return $result;
    }
    
    /**
     * Compress image with guaranteed maximum file size
     * Uses progressive quality reduction and dimension scaling
     * @param string $imagePath Path to the uploaded image
     * @param int $maxSizeBytes Maximum file size in bytes
     * @return string|false Compressed image path or false on failure
     */
    public function compressImageToGuaranteedSize($imagePath, $maxSizeBytes) {
        try {
            // Check if GD extension is available
            if (!extension_loaded('gd')) {
                // Fallback: If image is already under size limit, just rename it
                if (filesize($imagePath) <= $maxSizeBytes) {
                    $pathInfo = pathinfo($imagePath);
                    $finalPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_processed.' . $pathInfo['extension'];
                    if (copy($imagePath, $finalPath)) {
                        unlink($imagePath);
                        return $finalPath;
                    }
                }
                throw new Exception('GD extension is not available and image is too large. Please enable GD extension or use smaller images.');
            }

            $imageInfo = getimagesize($imagePath);
            if (!$imageInfo) {
                return false;
            }

            $mime = $imageInfo['mime'];
            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];

            // Create image resource based on type
            $source = $this->createImageResource($imagePath, $mime);
            if (!$source) {
                return false;
            }
        } catch (Exception $e) {
            // Log the error or handle it appropriately
            error_log("Image compression error: " . $e->getMessage());
            return false;
        }
        
        // Start with maximum dimensions and high quality
        $currentWidth = min($originalWidth, $this->maxWidth);
        $currentHeight = min($originalHeight, $this->maxHeight);
        $quality = 90;
        $attempt = 0;
        $maxAttempts = 20;
        
        // Calculate initial dimensions maintaining aspect ratio
        $ratio = min($this->maxWidth / $originalWidth, $this->maxHeight / $originalHeight);
        if ($ratio < 1) {
            $currentWidth = round($originalWidth * $ratio);
            $currentHeight = round($originalHeight * $ratio);
        }
        
        do {
            $attempt++;
            
            // Create resized image
            $destination = imagecreatetruecolor($currentWidth, $currentHeight);
            
            // Preserve transparency for PNG and GIF
            if ($mime == 'image/png' || $mime == 'image/gif') {
                imagealphablending($destination, false);
                imagesavealpha($destination, true);
                $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
                imagefilledrectangle($destination, 0, 0, $currentWidth, $currentHeight, $transparent);
            }
            
            // Resize image
            imagecopyresampled($destination, $source, 0, 0, 0, 0, $currentWidth, $currentHeight, $originalWidth, $originalHeight);
            
            // Generate temporary filename for testing
            $pathInfo = pathinfo($imagePath);
            $tempPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_temp_' . $attempt . '.jpg';
            
            // Save with current quality (always as JPEG for better compression)
            $success = imagejpeg($destination, $tempPath, $quality);
            imagedestroy($destination);
            
            if (!$success) {
                break;
            }
            
            $currentSize = filesize($tempPath);
            
            // If size is acceptable, use this version
            if ($currentSize <= $maxSizeBytes) {
                imagedestroy($source);
                
                // Generate final filename
                $finalPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_compressed.jpg';
                
                // Move temp file to final location
                if (rename($tempPath, $finalPath)) {
                    // Remove original file
                    unlink($imagePath);
                    return $finalPath;
                } else {
                    unlink($tempPath);
                    return false;
                }
            }
            
            // Clean up temp file
            unlink($tempPath);
            
            // Adjust parameters for next attempt
            if ($quality > $this->minQuality) {
                // Reduce quality first
                $quality -= 10;
            } else {
                // If quality is at minimum, reduce dimensions
                $currentWidth = round($currentWidth * 0.9);
                $currentHeight = round($currentHeight * 0.9);
                $quality = 80; // Reset quality when reducing dimensions
                
                // Prevent infinite loop with very small dimensions
                if ($currentWidth < 100 || $currentHeight < 100) {
                    break;
                }
            }
            
        } while ($attempt < $maxAttempts);
        
        imagedestroy($source);
        return false;
    }
    
    /**
     * Create image resource from file based on MIME type
     * @param string $imagePath Path to image file
     * @param string $mime MIME type
     * @return resource|false Image resource or false on failure
     */
    private function createImageResource($imagePath, $mime) {
        // Check if GD extension is loaded
        if (!extension_loaded('gd')) {
            throw new Exception('GD extension is not enabled. Please enable GD extension in your PHP configuration.');
        }

        // Check if specific functions exist
        switch($mime) {
            case 'image/jpeg':
                if (!function_exists('imagecreatefromjpeg')) {
                    throw new Exception('JPEG support is not available in GD extension.');
                }
                return imagecreatefromjpeg($imagePath);
            case 'image/png':
                if (!function_exists('imagecreatefrompng')) {
                    throw new Exception('PNG support is not available in GD extension.');
                }
                return imagecreatefrompng($imagePath);
            case 'image/gif':
                if (!function_exists('imagecreatefromgif')) {
                    throw new Exception('GIF support is not available in GD extension.');
                }
                return imagecreatefromgif($imagePath);
            case 'image/webp':
                if (function_exists('imagecreatefromwebp')) {
                    return imagecreatefromwebp($imagePath);
                } else {
                    throw new Exception('WebP support is not available in GD extension.');
                }
        }
        return false;
    }
    
    /**
     * Set display image for a destination
     * @param int $destinationId Destination ID
     * @param int $imageId Image ID to set as display
     * @return bool Success status
     */
    public function setDisplayImage($destinationId, $imageId) {
        $imageModel = new Image();
        $destinationModel = new Destination();
        
        // Reset all images for this destination
        $imageModel->updateByDestination($destinationId, ['is_display_image' => 0]);
        
        // Set the selected image as display
        $success = $imageModel->update($imageId, ['is_display_image' => 1]);
        
        if ($success) {
            // Update destination's display_image_id
            $destinationModel->update($destinationId, ['display_image_id' => $imageId]);
        }
        
        return $success;
    }

    /**
     * Set display image for a tour package
     * @param int $tourPackageId Tour Package ID
     * @param int $imageId Image ID to set as display
     * @return bool Success status
     */
    public function setDisplayImageForPackage($tourPackageId, $imageId) {
        $imageModel = new Image();
        $tourPackageModel = new TourPackage();

        // Reset all images for this tour package
        $imageModel->updateByTourPackage($tourPackageId, ['is_display_image' => 0]);

        // Set the selected image as display
        $success = $imageModel->update($imageId, ['is_display_image' => 1]);

        if ($success) {
            // Get current package data to preserve other fields
            $currentPackage = $tourPackageModel->findById($tourPackageId);
            if ($currentPackage) {
                $updateData = [
                    'package_type_id' => $currentPackage['package_type_id'],
                    'name' => $currentPackage['name'],
                    'description' => $currentPackage['description'],
                    'price' => $currentPackage['price'],
                    'duration' => $currentPackage['duration'],
                    'display_image_id' => $imageId
                ];
                $tourPackageModel->update($tourPackageId, $updateData);
            }
        }

        return $success;
    }

    /**
     * Delete image and associated file for tour package
     * @param int $imageId Image ID to delete
     * @param int $tourPackageId Tour Package ID for authorization
     * @return array Result with success status
     */
    public function deleteImageForPackage($imageId, $tourPackageId) {
        $result = ['success' => false, 'error' => ''];

        $imageModel = new Image();
        $image = $imageModel->findById($imageId);

        if (!$image) {
            $result['error'] = 'Image not found';
            return $result;
        }

        if ($image['tour_package_id'] != $tourPackageId) {
            $result['error'] = 'Unauthorized to delete this image';
            return $result;
        }

        // Delete file from filesystem
        if (file_exists($image['url'])) {
            unlink($image['url']);
        }

        // Delete from database
        if ($imageModel->delete($imageId)) {
            // If this was the display image, set another image as display
            if ($image['is_display_image']) {
                $this->reassignDisplayImageForPackage($tourPackageId);
            }

            $result['success'] = true;
        } else {
            $result['error'] = 'Failed to delete image from database';
        }

        return $result;
    }

    /**
     * Delete image and associated file
     * @param int $imageId Image ID to delete
     * @param int $destinationId Destination ID for authorization
     * @return array Result with success status
     */
    public function deleteImage($imageId, $destinationId) {
        $result = ['success' => false, 'error' => ''];
        
        $imageModel = new Image();
        $image = $imageModel->findById($imageId);
        
        if (!$image) {
            $result['error'] = 'Image not found';
            return $result;
        }
        
        if ($image['destination_id'] != $destinationId) {
            $result['error'] = 'Unauthorized to delete this image';
            return $result;
        }
        
        // Delete file from filesystem
        if (file_exists($image['url'])) {
            unlink($image['url']);
        }
        
        // Delete from database
        if ($imageModel->delete($imageId)) {
            // If this was the display image, set another image as display
            if ($image['is_display_image']) {
                $this->reassignDisplayImage($destinationId);
            }
            
            $result['success'] = true;
        } else {
            $result['error'] = 'Failed to delete image from database';
        }
        
        return $result;
    }
    
    /**
     * Reassign display image when current display image is deleted
     * @param int $destinationId Destination ID
     */
    private function reassignDisplayImage($destinationId) {
        $imageModel = new Image();
        $destinationModel = new Destination();
        
        $remainingImages = $imageModel->findByDestination($destinationId);
        
        if (!empty($remainingImages)) {
            $newDisplayImage = $remainingImages[0];
            $this->setDisplayImage($destinationId, $newDisplayImage['image_id']);
        } else {
            // No images left, clear display_image_id
            $destinationModel->update($destinationId, ['display_image_id' => null]);
        }
    }

    /**
     * Reassign display image when current display image is deleted for tour package
     * @param int $tourPackageId Tour Package ID
     */
    private function reassignDisplayImageForPackage($tourPackageId) {
        $imageModel = new Image();
        $tourPackageModel = new TourPackage();

        $remainingImages = $imageModel->findByTourPackage($tourPackageId);

        if (!empty($remainingImages)) {
            $newDisplayImage = $remainingImages[0];
            $this->setDisplayImageForPackage($tourPackageId, $newDisplayImage['image_id']);
        } else {
            // No images left, clear display_image_id
            $currentPackage = $tourPackageModel->findById($tourPackageId);
            if ($currentPackage) {
                $updateData = [
                    'package_type_id' => $currentPackage['package_type_id'],
                    'name' => $currentPackage['name'],
                    'description' => $currentPackage['description'],
                    'price' => $currentPackage['price'],
                    'duration' => $currentPackage['duration'],
                    'display_image_id' => null
                ];
                $tourPackageModel->update($tourPackageId, $updateData);
            }
        }
    }

    /**
     * Get upload error message
     * @param int $errorCode PHP upload error code
     * @return string Error message
     */
    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Generate thumbnail for an image
     * @param string $imagePath Path to the source image
     * @param int $width Thumbnail width
     * @param int $height Thumbnail height
     * @return string|false Thumbnail path or false on failure
     */
    public function generateThumbnail($imagePath, $width = 300, $height = 300) {
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }
        
        $mime = $imageInfo['mime'];
        
        // Create source image
        $source = $this->createImageResource($imagePath, $mime);
        if (!$source) {
            return false;
        }
        
        $originalWidth = imagesx($source);
        $originalHeight = imagesy($source);
        
        // Calculate crop dimensions for square thumbnail
        $cropSize = min($originalWidth, $originalHeight);
        $cropX = ($originalWidth - $cropSize) / 2;
        $cropY = ($originalHeight - $cropSize) / 2;
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($width, $height);
        
        // Preserve transparency
        if ($mime == 'image/png' || $mime == 'image/gif') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $width, $height, $transparent);
        }
        
        // Copy and resize
        imagecopyresampled($thumbnail, $source, 0, 0, $cropX, $cropY, $width, $height, $cropSize, $cropSize);
        
        // Generate thumbnail filename
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb_' . $width . 'x' . $height . '.jpg';
        
        // Save thumbnail (always as JPEG for consistency)
        $success = imagejpeg($thumbnail, $thumbnailPath, 85);
        
        imagedestroy($source);
        imagedestroy($thumbnail);
        
        return $success ? $thumbnailPath : false;
    }
    
    /**
     * Get image file size information
     * @param string $imagePath Path to image file
     * @return array Size information
     */
    public function getImageSizeInfo($imagePath) {
        if (!file_exists($imagePath)) {
            return ['error' => 'File not found'];
        }
        
        $fileSize = filesize($imagePath);
        $imageInfo = getimagesize($imagePath);
        
        if (!$imageInfo) {
            return ['error' => 'Invalid image file'];
        }
        
        return [
            'file_size' => $fileSize,
            'file_size_formatted' => $this->formatFileSize($fileSize),
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime'],
            'under_size_limit' => $fileSize <= $this->maxFileSize
        ];
    }
    
    /**
     * Format file size in human readable format
     * @param int $bytes File size in bytes
     * @return string Formatted file size
     */
    private function formatFileSize($bytes) {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    /**
     * Validate image file before processing
     * @param string $tmpName Temporary file path
     * @param string $fileName Original file name
     * @param int $fileSize File size
     * @return array Validation result
     */
    public function validateImageFile($tmpName, $fileName, $fileSize) {
        $result = ['valid' => true, 'errors' => []];

        // Check file extension
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedTypes)) {
            $result['valid'] = false;
            $result['errors'][] = "Invalid file type. Allowed: " . implode(', ', $this->allowedTypes);
        }

        // Check if file is actually an image
        $imageInfo = getimagesize($tmpName);
        if (!$imageInfo) {
            $result['valid'] = false;
            $result['errors'][] = "File is not a valid image";
        }

        // Note: We don't check file size here since we guarantee compression to 5MB

        return $result;
    }

    // ========== GALLERY-SPECIFIC METHODS (from root ImageManager.php) ==========

    /**
     * Process gallery image uploads (from root version)
     * @param array $files $_FILES array for multiple files
     * @param int $userId User ID who uploaded the images
     * @param string $title Title for alt text generation
     * @param string $altText Alt text for images
     * @param string $description Description for images
     * @param string $category Image category (e.g., 'main_gallery')
     * @param int $displayImageIndex Index of image to set as display (optional)
     * @return array Result with success status and details
     */
    public function processGalleryUploads($files, $userId, $altText, $description, $category, $displayImageIndex = null) {
        $result = [
            'success' => false,
            'uploaded_images' => [],
            'errors' => []
        ];

        if (!isset($files['name']) || !is_array($files['name'])) {
            $result['errors'][] = 'No files provided';
            return $result;
        }

        $imageModel = new Image();
        $uploadedCount = 0;

        // If setting a display image, first unset all display images in this category
        if ($displayImageIndex !== null) {
            $imageModel->unsetDisplayImages($category);
        }

        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                $result['errors'][] = "Upload error for file " . ($i + 1);
                continue;
            }

            $uploadResult = $this->processSingleGalleryUpload(
                $files['tmp_name'][$i],
                $files['name'][$i],
                $files['type'][$i],
                $files['size'][$i],
                $userId,
                $altText,
                $description,
                $category,
                ($i === $displayImageIndex)
            );

            if ($uploadResult['success']) {
                $result['uploaded_images'][] = $uploadResult['image'];
                $uploadedCount++;
            } else {
                $result['errors'][] = $uploadResult['error'];
            }
        }

        $result['success'] = $uploadedCount > 0;
        return $result;
    }

    /**
     * Process single gallery image upload (from root version)
     * @param string $tmpName Temporary file path
     * @param string $originalName Original file name
     * @param string $mimeType MIME type
     * @param int $fileSize File size
     * @param int $userId User ID
     * @param string $title Title for alt text
     * @param string $altText Alt text
     * @param string $description Description
     * @param string $category Image category
     * @param bool $isDisplayImage Whether this should be the display image
     * @return array Result with success status and image data
     */
    private function processSingleGalleryUpload($tmpName, $originalName, $mimeType, $fileSize, $userId, $altText, $description, $category, $isDisplayImage = false) {
        $result = [
            'success' => false,
            'image' => null,
            'error' => ''
        ];

        // Validate file type
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $result['error'] = "Invalid file type for {$originalName}";
            return $result;
        }

        // Validate file size
        if ($fileSize > $this->maxFileSize) {
            $result['error'] = "File {$originalName} is too large";
            return $result;
        }

        // Generate unique filename
        $extension = $this->getFileExtension($originalName);
        $filename = $this->generateUniqueFilename($extension);

        // Use consistent upload directory structure for gallery images
        $uploadDir = 'upload/images/main-gallery/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $filepath = $uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($tmpName, $filepath)) {
            $result['error'] = "Failed to move uploaded file {$originalName}";
            return $result;
        }

        // Save to database with web-accessible URL
        $imageModel = new Image();
        $webUrl = 'admin-dashboard/upload/images/main-gallery/' . $filename; // Web-accessible path
        $imageData = [
            'url' => $webUrl,
            'alt_text' => $altText,
            'is_display_image' => $isDisplayImage ? 1 : 0,
            'image_category' => $category,
            'destination_id' => null,
            'tour_package_id' => null,
            'gallery_id' => null,
            'uploaded_by_user_id' => $userId
        ];

        $imageId = $imageModel->create($imageData);

        if ($imageId) {
            $result['success'] = true;
            $result['image'] = array_merge($imageData, ['image_id' => $imageId]);
        } else {
            // Clean up uploaded file if database insert failed
            unlink($filepath);
            $result['error'] = "Failed to save image data for {$originalName}";
        }

        return $result;
    }

    /**
     * Set display image for a category (from root version)
     * @param int $imageId Image ID to set as display
     * @param string $category Image category
     * @return bool Success status
     */
    public function setDisplayImageForCategory($imageId, $category) {
        $imageModel = new Image();

        // First unset all display images in this category
        $imageModel->unsetDisplayImages($category);

        // Then set this image as display
        return $imageModel->update($imageId, ['is_display_image' => 1]);
    }

    /**
     * Delete an image by ID (from root version, enhanced)
     * @param int $imageId Image ID to delete
     * @return array Result with success status
     */
    public function deleteImageById($imageId) {
        $imageModel = new Image();
        $image = $imageModel->findById($imageId);

        if (!$image) {
            return ['success' => false, 'error' => 'Image not found'];
        }

        // Delete physical file
        if (file_exists($image['url'])) {
            unlink($image['url']);
        }

        // Delete from database
        if ($imageModel->delete($imageId)) {
            return ['success' => true];
        } else {
            return ['success' => false, 'error' => 'Failed to delete image from database'];
        }
    }

    /**
     * Get file extension from filename (from root version)
     * @param string $filename Original filename
     * @return string File extension
     */
    private function getFileExtension($filename) {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }

    /**
     * Generate unique filename (from root version)
     * @param string $extension File extension
     * @return string Unique filename
     */
    private function generateUniqueFilename($extension) {
        do {
            $filename = uniqid('img_', true) . '.' . $extension;
        } while (file_exists($this->uploadDir . $filename) ||
                 file_exists('../uploads/' . $filename) ||
                 file_exists('../uploads/images/main-gallery/' . $filename));

        return $filename;
    }
}
?>

