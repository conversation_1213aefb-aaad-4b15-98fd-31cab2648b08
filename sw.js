// Service Worker for Meleva Tours and Travel
// Provides caching for better performance and offline functionality

const CACHE_NAME = 'meleva-tours-v1';
const STATIC_CACHE_URLS = [
    '/',
    '/style.css',
    '/js/global.js',
    '/js/image-optimizer.js',
    '/images/meleva-lg.png',
    '/images/hero-bg.jpg',
    '/favicon.ico',
    '/favicon.png'
];

// Install event - cache static assets
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching static assets');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .catch(error => {
                console.error('Failed to cache static assets:', error);
            })
    );
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    self.clients.claim();
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', event => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip admin dashboard requests
    if (event.request.url.includes('/admin-dashboard/')) {
        return;
    }
    
    // Skip API requests
    if (event.request.url.includes('/api/')) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached version if available
                if (response) {
                    return response;
                }
                
                // Otherwise fetch from network
                return fetch(event.request)
                    .then(response => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Clone the response
                        const responseToCache = response.clone();
                        
                        // Cache static assets and HTML pages
                        if (shouldCache(event.request.url)) {
                            caches.open(CACHE_NAME)
                                .then(cache => {
                                    cache.put(event.request, responseToCache);
                                });
                        }
                        
                        return response;
                    })
                    .catch(error => {
                        console.error('Fetch failed:', error);
                        
                        // Return offline page for HTML requests
                        if (event.request.headers.get('accept').includes('text/html')) {
                            return new Response(
                                '<html><body><h1>Offline</h1><p>Please check your internet connection.</p></body></html>',
                                { headers: { 'Content-Type': 'text/html' } }
                            );
                        }
                    });
            })
    );
});

// Helper function to determine if a URL should be cached
function shouldCache(url) {
    // Cache CSS, JS, images, and HTML pages
    return url.match(/\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|eot)$/) ||
           url.match(/\.(php|html)$/) ||
           url.endsWith('/');
}

// Background sync for form submissions (if needed)
self.addEventListener('sync', event => {
    if (event.tag === 'quote-request') {
        event.waitUntil(syncQuoteRequests());
    }
});

// Sync quote requests when back online
async function syncQuoteRequests() {
    try {
        // Implementation would depend on your offline storage strategy
        console.log('Syncing quote requests...');
    } catch (error) {
        console.error('Failed to sync quote requests:', error);
    }
}
