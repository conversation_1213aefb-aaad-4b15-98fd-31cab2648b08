<?php
/**
 * Breadcrumb Generator for Meleva Tours and Travel
 * Provides consistent breadcrumb navigation across all pages
 */

class Breadcrumbs {
    private $baseUrl;
    private $breadcrumbs = [];
    
    public function __construct() {
        $this->baseUrl = $this->getBaseUrl();
        $this->addBreadcrumb('Home', $this->baseUrl . '/');
    }
    
    public function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            return $protocol . '://' . $host . '/meleva';
        } else {
            return $protocol . '://' . $host;
        }
    }
    
    public function addBreadcrumb($title, $url = null) {
        $this->breadcrumbs[] = [
            'title' => $title,
            'url' => $url
        ];
    }
    
    public function generateBreadcrumbs($currentPage = '') {
        // Auto-generate breadcrumbs based on current page if not manually set
        if (empty($this->breadcrumbs) || count($this->breadcrumbs) === 1) {
            $this->autoGenerateBreadcrumbs($currentPage);
        }
        
        if (count($this->breadcrumbs) <= 1) {
            return ''; // Don't show breadcrumbs for homepage only
        }
        
        $html = '<nav class="breadcrumbs mb-8" aria-label="Breadcrumb">';
        $html .= '<ol class="flex items-center space-x-2 text-sm text-gray-600">';
        
        foreach ($this->breadcrumbs as $index => $breadcrumb) {
            $isLast = ($index === count($this->breadcrumbs) - 1);
            
            $html .= '<li class="flex items-center">';
            
            if ($index > 0) {
                $html .= '<svg class="w-4 h-4 mx-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">';
                $html .= '<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>';
                $html .= '</svg>';
            }
            
            if ($isLast || !$breadcrumb['url']) {
                $html .= '<span class="text-gray-900 font-medium">' . htmlspecialchars($breadcrumb['title']) . '</span>';
            } else {
                $html .= '<a href="' . htmlspecialchars($breadcrumb['url']) . '" class="text-orange-500 hover:text-orange-600 transition duration-300">';
                $html .= htmlspecialchars($breadcrumb['title']);
                $html .= '</a>';
            }
            
            $html .= '</li>';
        }
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
    
    private function autoGenerateBreadcrumbs($currentPage) {
        $page = basename($_SERVER['PHP_SELF']);
        
        switch ($page) {
            case 'about.php':
                $this->addBreadcrumb('About Us');
                break;
                
            case 'tours.php':
                $this->addBreadcrumb('Tours & Destinations');
                break;
                
            case 'gallery.php':
                $this->addBreadcrumb('Gallery');
                break;
                
            case 'contact.php':
                $this->addBreadcrumb('Contact');
                break;
                
            case 'request-quote.php':
                $this->addBreadcrumb('Request Quote');
                break;
                
            case 'destination-details.php':
                $this->addBreadcrumb('Tours & Destinations', $this->baseUrl . '/tours.php');
                if (!empty($currentPage)) {
                    $this->addBreadcrumb($currentPage);
                } else {
                    $this->addBreadcrumb('Destination Details');
                }
                break;
                
            case 'package-details.php':
                $this->addBreadcrumb('Tours & Destinations', $this->baseUrl . '/tours.php');
                if (!empty($currentPage)) {
                    $this->addBreadcrumb($currentPage);
                } else {
                    $this->addBreadcrumb('Package Details');
                }
                break;
                
            case 'privacy-policy.php':
                $this->addBreadcrumb('Privacy Policy');
                break;
                
            case 'terms-of-service.php':
                $this->addBreadcrumb('Terms of Service');
                break;
                
            case 'cookie-policy.php':
                $this->addBreadcrumb('Cookie Policy');
                break;
                
            case 'payment.php':
                $this->addBreadcrumb('Payment');
                break;
                
            case 'payment-success.php':
                $this->addBreadcrumb('Payment', $this->baseUrl . '/payment.php');
                $this->addBreadcrumb('Payment Successful');
                break;
                
            case 'payment-failed.php':
                $this->addBreadcrumb('Payment', $this->baseUrl . '/payment.php');
                $this->addBreadcrumb('Payment Failed');
                break;
        }
    }
    
    public function generateStructuredData() {
        if (count($this->breadcrumbs) <= 1) {
            return '';
        }
        
        $itemList = [];
        foreach ($this->breadcrumbs as $index => $breadcrumb) {
            $itemList[] = [
                "@type" => "ListItem",
                "position" => $index + 1,
                "name" => $breadcrumb['title'],
                "item" => $breadcrumb['url'] ?? null
            ];
        }
        
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "BreadcrumbList",
            "itemListElement" => $itemList
        ];
        
        return '<script type="application/ld+json">' . json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . '</script>';
    }
}
