<?php
// Dynamic Sitemap Generator for Meleva Tours and Travel
header('Content-Type: application/xml; charset=utf-8');

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';

// Get the base URL dynamically
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    // Check if we're on localhost (development) or live server
    if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
        return $protocol . '://' . $host . '/meleva';
    } else {
        return $protocol . '://' . $host;
    }
}

$baseUrl = getBaseUrl();

// Initialize models
try {
    $destinationModel = new Destination();
    $tourPackageModel = new TourPackage();
    
    // Fetch all destinations and packages
    $destinations = $destinationModel->findAll();
    $packages = $tourPackageModel->findAll();
} catch (Exception $e) {
    $destinations = [];
    $packages = [];
    error_log("Sitemap generation error: " . $e->getMessage());
}

echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
    
    <!-- Static Pages -->
    <url>
        <loc><?php echo $baseUrl; ?>/</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/about.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/tours.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/gallery.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.7</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/contact.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/request-quote.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.9</priority>
    </url>
    
    <!-- Policy Pages -->
    <url>
        <loc><?php echo $baseUrl; ?>/privacy-policy.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>yearly</changefreq>
        <priority>0.3</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/terms-of-service.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>yearly</changefreq>
        <priority>0.3</priority>
    </url>
    
    <url>
        <loc><?php echo $baseUrl; ?>/cookie-policy.php</loc>
        <lastmod><?php echo date('Y-m-d'); ?></lastmod>
        <changefreq>yearly</changefreq>
        <priority>0.3</priority>
    </url>
    
    <!-- Dynamic Destination Pages -->
    <?php foreach ($destinations as $destination): ?>
    <url>
        <loc><?php echo $baseUrl; ?>/destination-details.php?id=<?php echo $destination['destination_id']; ?></loc>
        <lastmod><?php echo date('Y-m-d', strtotime($destination['updated_at'] ?? $destination['created_at'] ?? 'now')); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; ?>
    
    <!-- Dynamic Package Pages -->
    <?php foreach ($packages as $package): ?>
    <url>
        <loc><?php echo $baseUrl; ?>/package-details.php?id=<?php echo $package['tour_package_id']; ?></loc>
        <lastmod><?php echo date('Y-m-d', strtotime($package['updated_at'] ?? $package['created_at'] ?? 'now')); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; ?>
    
</urlset>
