<?php
// Start output buffering to prevent header issues
ob_start();

// Define admin access constant
define('ADMIN_ACCESS', true);

require_once '../config/config.php';
require_once '../classes/models.php';

// Simple authentication check without CSRF for AJAX
Auth::startSession();
Auth::requireLogin();

// Check admin role
$user = Auth::getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Insufficient permissions']);
    exit;
}

header('Content-Type: application/json');

// Enable error logging for debugging
error_log("API called: " . $_SERVER['REQUEST_METHOD'] . " " . $_SERVER['REQUEST_URI']);

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

// Debug logging
error_log("Raw input: " . $rawInput);
error_log("Decoded input: " . print_r($input, true));

if (!isset($input['message_id']) || !is_numeric($input['message_id'])) {
    error_log("Invalid message ID: " . print_r($input, true));
    echo json_encode(['success' => false, 'error' => 'Invalid message ID']);
    exit;
}

if (!isset($input['action']) || !in_array($input['action'], ['mark_read', 'mark_unread'])) {
    error_log("Invalid action: " . print_r($input, true));
    echo json_encode(['success' => false, 'error' => 'Invalid action']);
    exit;
}

$messageId = intval($input['message_id']);
$action = $input['action'];
$messageModel = new Message();

try {
    error_log("Processing action: $action for message ID: $messageId");

    if ($action === 'mark_read') {
        $success = $messageModel->markAsRead($messageId);
        $newStatus = 1; // read
        error_log("Mark as read result: " . ($success ? 'success' : 'failed'));
    } else {
        // Mark as unread
        $sql = "UPDATE messages SET is_read = 0 WHERE message_id = :id";
        $stmt = $messageModel->getDb()->prepare($sql);
        $stmt->bindParam(':id', $messageId);
        $success = $stmt->execute();
        $newStatus = 0; // unread
        error_log("Mark as unread result: " . ($success ? 'success' : 'failed'));

        if (!$success) {
            $errorInfo = $stmt->errorInfo();
            error_log("SQL Error: " . print_r($errorInfo, true));
        }
    }
    
    if ($success) {
        // Get updated unread count
        $stats = $messageModel->getStats();
        
        echo json_encode([
            'success' => true,
            'message' => 'Message status updated successfully',
            'new_status' => $newStatus,
            'unread_count' => $stats['unread']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to update message status'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
