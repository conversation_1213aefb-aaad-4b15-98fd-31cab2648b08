<?php
// Image Sitemap Generator for Meleva Tours and Travel
header('Content-Type: application/xml; charset=utf-8');

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';

// Get the base URL dynamically
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    // Check if we're on localhost (development) or live server
    if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
        return $protocol . '://' . $host . '/meleva';
    } else {
        return $protocol . '://' . $host;
    }
}

$baseUrl = getBaseUrl();

// Initialize models
try {
    $imageModel = new Image();
    $destinationModel = new Destination();
    $tourPackageModel = new TourPackage();
    
    // Fetch all images with their associated content
    $allImages = $imageModel->findAll();
    $destinations = $destinationModel->findAll();
    $packages = $tourPackageModel->findAll();
    
    // Create lookup arrays for better performance
    $destinationLookup = [];
    foreach ($destinations as $dest) {
        $destinationLookup[$dest['destination_id']] = $dest;
    }
    
    $packageLookup = [];
    foreach ($packages as $pkg) {
        $packageLookup[$pkg['tour_package_id']] = $pkg;
    }
    
} catch (Exception $e) {
    $allImages = [];
    $destinationLookup = [];
    $packageLookup = [];
    error_log("Image sitemap generation error: " . $e->getMessage());
}

echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
    
    <!-- Homepage with hero image -->
    <url>
        <loc><?php echo $baseUrl; ?>/</loc>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/images/hero-bg.jpg</image:loc>
            <image:caption>African Safari Adventure - Meleva Tours</image:caption>
            <image:title>Authentic African Safari Adventures</image:title>
        </image:image>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/images/meleva-lg.png</image:loc>
            <image:caption>Meleva Tours and Travel Logo</image:caption>
            <image:title>Meleva Tours Logo</image:title>
        </image:image>
    </url>
    
    <!-- Gallery page with all gallery images -->
    <url>
        <loc><?php echo $baseUrl; ?>/gallery.php</loc>
        <?php 
        $galleryImages = array_filter($allImages, function($img) {
            return $img['image_category'] === 'main_gallery';
        });
        foreach ($galleryImages as $image): 
        ?>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/<?php echo htmlspecialchars($image['url']); ?></image:loc>
            <image:caption><?php echo htmlspecialchars($image['alt_text'] ?? 'Gallery Image - Meleva Tours'); ?></image:caption>
            <image:title><?php echo htmlspecialchars($image['alt_text'] ?? 'African Safari Gallery'); ?></image:title>
        </image:image>
        <?php endforeach; ?>
    </url>
    
    <!-- Destination pages with their images -->
    <?php foreach ($destinationLookup as $destId => $destination): ?>
    <url>
        <loc><?php echo $baseUrl; ?>/destination-details.php?id=<?php echo $destId; ?></loc>
        <?php 
        $destImages = array_filter($allImages, function($img) use ($destId) {
            return $img['destination_id'] == $destId;
        });
        foreach ($destImages as $image): 
        ?>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/admin-dashboard/<?php echo htmlspecialchars($image['url']); ?></image:loc>
            <image:caption><?php echo htmlspecialchars($destination['name'] . ' - ' . ($image['alt_text'] ?? 'Destination Image')); ?></image:caption>
            <image:title><?php echo htmlspecialchars($destination['name'] . ' Safari Destination'); ?></image:title>
        </image:image>
        <?php endforeach; ?>
    </url>
    <?php endforeach; ?>
    
    <!-- Package pages with their images -->
    <?php foreach ($packageLookup as $pkgId => $package): ?>
    <url>
        <loc><?php echo $baseUrl; ?>/package-details.php?id=<?php echo $pkgId; ?></loc>
        <?php 
        $pkgImages = array_filter($allImages, function($img) use ($pkgId) {
            return $img['tour_package_id'] == $pkgId;
        });
        foreach ($pkgImages as $image): 
        ?>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/admin-dashboard/<?php echo htmlspecialchars($image['url']); ?></image:loc>
            <image:caption><?php echo htmlspecialchars($package['name'] . ' - ' . ($image['alt_text'] ?? 'Tour Package Image')); ?></image:caption>
            <image:title><?php echo htmlspecialchars($package['name'] . ' Safari Package'); ?></image:title>
        </image:image>
        <?php endforeach; ?>
    </url>
    <?php endforeach; ?>
    
    <!-- Tours page with featured images -->
    <url>
        <loc><?php echo $baseUrl; ?>/tours.php</loc>
        <?php 
        // Include display images from destinations and packages
        foreach ($destinationLookup as $destination) {
            if (!empty($destination['display_image_url'])):
        ?>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/admin-dashboard/<?php echo htmlspecialchars($destination['display_image_url']); ?></image:loc>
            <image:caption><?php echo htmlspecialchars($destination['name'] . ' - Featured Destination'); ?></image:caption>
            <image:title><?php echo htmlspecialchars($destination['name'] . ' Safari Destination'); ?></image:title>
        </image:image>
        <?php 
            endif;
        }
        
        foreach ($packageLookup as $package) {
            if (!empty($package['display_image_url'])):
        ?>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?></image:loc>
            <image:caption><?php echo htmlspecialchars($package['name'] . ' - Featured Package'); ?></image:caption>
            <image:title><?php echo htmlspecialchars($package['name'] . ' Safari Package'); ?></image:title>
        </image:image>
        <?php 
            endif;
        }
        ?>
    </url>
    
</urlset>
