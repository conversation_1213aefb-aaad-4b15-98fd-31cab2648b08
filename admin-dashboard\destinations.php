<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';
require_once 'classes/ImageManager.php';

// Require authentication
Auth::requireLogin();

$destinationModel = new Destination();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar (to provide $stats['unread_messages'])
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                try {
                    $data = [
                        'name' => Utils::sanitizeInput($_POST['name']),
                        'location' => Utils::sanitizeInput($_POST['location']),
                        'price' => floatval($_POST['price']),
                        'short_description' => Utils::sanitizeInput($_POST['short_description']),

                        'created_by_user_id' => $currentUser['user_id']
                    ];

                    if ($destinationModel->create($data)) {
                        $destinationId = $destinationModel->getLastInsertedId();
                        $uploadedImages = [];

                        // Handle multiple image uploads using ImageManager
                        if (isset($_FILES["destination_images"]) && !empty($_FILES["destination_images"]["name"][0])) {
                            $imageManager = new ImageManager();
                            $displayImageIndex = isset($_POST['display_image_index']) ? intval($_POST['display_image_index']) : null;

                            $uploadResult = $imageManager->processMultipleUploads(
                                $_FILES["destination_images"],
                                $destinationId,
                                $currentUser['user_id'],
                                Utils::sanitizeInput($_POST['name']),
                                $displayImageIndex
                            );

                            if ($uploadResult['success']) {
                                $uploadedImages = $uploadResult['uploaded_images'];

                                // Update destination with display image
                                if ($uploadResult['display_image_id']) {
                                    $destinationModel->update($destinationId, ["display_image_id" => $uploadResult['display_image_id']]);
                                }

                                $success = "Destination created successfully with " . count($uploadedImages) . " image(s)!";

                                if (!empty($uploadResult['errors'])) {
                                    $success .= " Some files had issues: " . implode(', ', $uploadResult['errors']);
                                }
                            } else {
                                $error = "Destination created but image upload failed: " . implode(', ', $uploadResult['errors']);
                            }
                        } else {
                            $success = "Destination created successfully (no images uploaded).";
                        }
                    } else {
                        $error = "Failed to create destination.";
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;
                
            case 'update':
                try {
                    $id = intval($_POST['id']);
                    $data = [
                        'name' => Utils::sanitizeInput($_POST['name']),
                        'location' => Utils::sanitizeInput($_POST['location']),
                        'price' => floatval($_POST['price']),
                        'short_description' => Utils::sanitizeInput($_POST['short_description']),

                    ];

                    $uploadedImages = [];

                    // Handle new image uploads for update using ImageManager
                    if (isset($_FILES['destination_images']) && !empty($_FILES['destination_images']['name'][0])) {
                        $imageManager = new ImageManager();
                        $displayImageIndex = isset($_POST['display_image_index']) ? intval($_POST['display_image_index']) : null;

                        $uploadResult = $imageManager->processMultipleUploads(
                            $_FILES['destination_images'],
                            $id,
                            $currentUser['user_id'],
                            Utils::sanitizeInput($_POST['name']),
                            $displayImageIndex
                        );

                        if ($uploadResult['success']) {
                            $uploadedImages = $uploadResult['uploaded_images'];

                            // Update destination with display image if new one was set
                            if ($uploadResult['display_image_id']) {
                                $data['display_image_id'] = $uploadResult['display_image_id'];
                            }
                        } else {
                            $error = 'Failed to upload new images: ' . implode(', ', $uploadResult['errors']);
                        }
                    }

                    // Handle display image selection from existing images
                    if (isset($_POST['display_image_id']) && !empty($_POST['display_image_id']) && empty($uploadedImages)) {
                        $displayImageId = intval($_POST['display_image_id']);
                        $imageManager = new ImageManager();

                        if ($imageManager->setDisplayImage($id, $displayImageId)) {
                            $data['display_image_id'] = $displayImageId;
                        }
                    }

                    if ($destinationModel->update($id, $data)) {
                        $success = 'Destination updated successfully!';
                        if (!empty($uploadedImages)) {
                            $success .= ' Added ' . count($uploadedImages) . ' new image(s).';
                        }
                    } else {
                        $error = 'Failed to update destination.';
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;
                
            case 'delete':
                $id = intval($_POST['id']);
                if ($destinationModel->delete($id)) {
                    $success = 'Destination deleted successfully!';
                } else {
                    $error = 'Failed to delete destination.';
                }
                break;

            case 'set_display_image':
                $imageId = intval($_POST['image_id']);
                $destinationId = intval($_POST['destination_id']);

                $imageManager = new ImageManager();
                $result = $imageManager->setDisplayImage($destinationId, $imageId);

                if ($result) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Failed to set display image']);
                }
                exit;

            case 'delete_image':
                $imageId = intval($_POST['image_id']);
                $destinationId = intval($_POST['destination_id']);

                $imageManager = new ImageManager();
                $result = $imageManager->deleteImage($imageId, $destinationId);

                if ($result['success']) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false, 'error' => $result['error']]);
                }
                exit;
        }
    }
}

// Get all destinations
$destinations = $destinationModel->findAllWithImages();

// Get destination for editing if ID is provided
$editDestination = null;
if (isset($_GET['edit'])) {
    $editDestination = $destinationModel->findById(intval($_GET['edit']));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Destinations - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .sidebar-transition {
            transition: all 0.3s ease;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .sidebar-active {
            background-color: rgba(249, 115, 22, 0.1);
            border-right: 3px solid #f97316;
        }

        /* Image Upload Styles */
        .upload-zone {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        
        .upload-zone.dragover {
            border-color: #f97316;
            background-color: rgba(249, 115, 22, 0.05);
        }
        
        .image-preview {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .image-preview:hover {
            transform: scale(1.02);
        }
        
        .image-preview img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .image-preview:hover .image-overlay {
            opacity: 1;
        }
        
        .compression-info {
            font-size: 0.75rem;
            color: #6b7280;
        }
        
        .display-image-indicator {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #f97316;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Form Styles */
        .form-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }

        .form-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px 12px 0 0;
            margin: -1px -1px 0 -1px;
            transition: all 0.3s ease;
        }

        .form-header:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-1px);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.expanded {
            max-height: 2000px;
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Destinations</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6">
            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Add Destination Form (Inline) -->
            <div class="form-section mb-6">
                <div class="form-header cursor-pointer" id="toggleAddFormHeader">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-plus mr-2"></i>Add New Destination
                        </h3>
                        <div class="text-white hover:text-orange-200 transition-colors">
                            <i class="fas fa-chevron-down transform transition-transform" id="toggleIcon"></i>
                        </div>
                    </div>
                </div>
                
                <div id="addFormContent" class="collapsible-content">
                    <div class="p-6">
                        <form id="addDestinationForm" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="create">
                            <input type="hidden" name="display_image_index" id="addDisplayImageIndex" value="">
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Left Column - Form Fields -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Destination Name *</label>
                                        <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Location *</label>
                                        <input type="text" name="location" required placeholder="e.g., Kenya, Tanzania, Uganda" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
                                        <input type="number" name="price" step="0.01" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Short Description *</label>
                                        <textarea name="short_description" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"></textarea>
                                    </div>
                                    

                                </div>
                                
                                <!-- Right Column - Image Upload -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Images</label>
                                        
                                        <!-- Upload Zone -->
                                        <div class="upload-zone border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-orange-500 transition-colors">
                                            <div class="space-y-2">
                                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium text-orange-600">Click to upload</span> or drag and drop
                                                </p>
                                                <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP (auto-compressed to max 5MB)</p>
                                            </div>
                                            <input type="file" id="addImageInput" name="destination_images[]" multiple accept="image/*" class="hidden">
                                        </div>
                                        
                                        <!-- Compression Progress -->
                                        <div id="addCompressionProgress" class="hidden mt-2">
                                            <div class="bg-gray-200 rounded-full h-2">
                                                <div id="addProgressBar" class="bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Compressing images...</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Image Previews -->
                                    <div id="addImagePreviewContainer" class="space-y-4">
                                        <div id="addImagePreviewGrid" class="grid grid-cols-2 gap-3"></div>
                                    </div>
                                    
                                    <!-- Upload Summary -->
                                    <div id="addUploadSummary" class="hidden bg-blue-50 p-3 rounded-lg">
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-blue-700">Total files: <span id="addTotalFiles">0</span></span>
                                            <span class="text-blue-700">Total size: <span id="addTotalSize">0 KB</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                                <button type="button" onclick="resetAddForm()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                    Reset
                                </button>
                                <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-plus mr-2"></i>Create Destination
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Destinations Table -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden card-hover">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">All Destinations</h3>
                    <p class="text-sm text-gray-500 mt-1">Manage your travel destinations</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (empty($destinations)): ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                        <i class="fas fa-map-marker-alt text-3xl mb-2"></i>
                                        <p>No destinations found. Create your first destination above!</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($destinations as $destination): ?>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex flex-col items-center justify-center">
                                                <?php if ($destination['display_image_url']): ?>
                                                    <img class="h-16 w-16 rounded-lg object-cover border border-gray-200 shadow-sm" src="<?php echo htmlspecialchars($destination['display_image_url']); ?>" alt="<?php echo htmlspecialchars($destination['name']); ?>" title="Display image for <?php echo htmlspecialchars($destination['name']); ?>">
                                                <?php else: ?>
                                                    <div class="h-16 w-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center shadow-sm mb-1">
                                                        <i class="fas fa-map-marker-alt text-white text-xl"></i>
                                                    </div>
                                                    <div class="text-xs text-gray-400 text-center">
                                                        <i class="fas fa-image mr-1"></i>No display image
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo Utils::displayText($destination['name']); ?></div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo Utils::displayText($destination['location'] ?: 'Not specified'); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-semibold text-gray-900">$<?php echo number_format($destination['price'], 2); ?></div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 max-w-xs"><?php echo Utils::truncateText($destination['short_description'], 100); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo Utils::displayText($destination['created_by'] ?? 'Unknown'); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button onclick="editDestination(<?php echo $destination['destination_id']; ?>)" class="text-orange-600 hover:text-orange-900 mr-3 transition-colors" title="Edit destination">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button onclick="deleteDestination(<?php echo $destination['destination_id']; ?>)" class="text-red-600 hover:text-red-900 transition-colors" title="Delete destination">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Edit Modal (Only for editing) -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-edit mr-2 text-orange-600"></i>Edit Destination
                        </h3>
                        <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="editDestinationForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" id="editDestinationId">
                        <input type="hidden" name="display_image_index" id="editDisplayImageIndex" value="">
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column - Form Fields -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Destination Name *</label>
                                    <input type="text" name="name" id="editDestinationName" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Location *</label>
                                    <input type="text" name="location" id="editDestinationLocation" required placeholder="e.g., Kenya, Tanzania, Uganda" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
                                    <input type="number" name="price" id="editDestinationPrice" step="0.01" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Short Description *</label>
                                    <textarea name="short_description" id="editShortDescription" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"></textarea>
                                </div>
                                

                            </div>
                            
                            <!-- Right Column - Image Upload -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New Images</label>
                                    
                                    <!-- Upload Zone -->
                                    <div class="upload-zone border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-orange-500 transition-colors">
                                        <div class="space-y-2">
                                            <i class="fas fa-cloud-upload-alt text-2xl text-gray-400"></i>
                                            <p class="text-sm text-gray-600">
                                                <span class="font-medium text-orange-600">Click to upload</span> new images
                                            </p>
                                            <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP (auto-compressed to max 5MB)</p>
                                        </div>
                                        <input type="file" id="editImageInput" name="destination_images[]" multiple accept="image/*" class="hidden">
                                    </div>
                                    
                                    <!-- Compression Progress -->
                                    <div id="editCompressionProgress" class="hidden mt-2">
                                        <div class="bg-gray-200 rounded-full h-2">
                                            <div id="editProgressBar" class="bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-1">Compressing images...</p>
                                    </div>
                                </div>
                                
                                <!-- New Image Previews -->
                                <div id="editImagePreviewContainer" class="space-y-4">
                                    <div id="editImagePreviewGrid" class="grid grid-cols-2 gap-3"></div>
                                    
                                    <!-- Upload Summary -->
                                    <div id="editUploadSummary" class="hidden bg-blue-50 p-3 rounded-lg">
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-blue-700">New files: <span id="editTotalFiles">0</span></span>
                                            <span class="text-blue-700">Total size: <span id="editTotalSize">0 KB</span></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Existing Images -->
                                <div id="existingImagesContainer" class="hidden">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Existing Images</h4>
                                    <div id="existingImagesGrid" class="grid grid-cols-2 gap-3"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                            <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                <i class="fas fa-save mr-2"></i>Update Destination
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let addSelectedFiles = [];
        let editSelectedFiles = [];
        let existingImages = [];
        let addDisplayImageIndex = -1;
        let editDisplayImageIndex = -1;

        // Destination data for JavaScript (decode HTML entities for proper display)
        const destinations = <?php
            // Decode HTML entities in the data before JSON encoding
            $decodedDestinations = array_map(function($destination) {
                if (isset($destination['name'])) {
                    $destination['name'] = html_entity_decode($destination['name'], ENT_QUOTES, 'UTF-8');
                }
                if (isset($destination['location'])) {
                    $destination['location'] = html_entity_decode($destination['location'], ENT_QUOTES, 'UTF-8');
                }
                if (isset($destination['short_description'])) {
                    $destination['short_description'] = html_entity_decode($destination['short_description'], ENT_QUOTES, 'UTF-8');
                }

                return $destination;
            }, $destinations);
            echo json_encode($decodedDestinations, JSON_UNESCAPED_UNICODE | JSON_HEX_QUOT | JSON_HEX_APOS);
        ?>;
        
        // Mobile sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            // Toggle sidebar on mobile
            function toggleSidebar() {
                sidebar.classList.toggle('sidebar-open');
                sidebarOverlay.classList.toggle('active');
                document.body.classList.toggle('overflow-hidden');
            }
            
            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }
            
            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }
            
            // Close sidebar when clicking on navigation links on mobile
            const navLinks = sidebar.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
            
            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });

            // Initialize form functionality
            initializeAddForm();
            initializeEditForm();
            initializeFormToggle();
        });

        // Form toggle functionality
        function initializeFormToggle() {
            const toggleHeader = document.getElementById('toggleAddFormHeader');
            const formContent = document.getElementById('addFormContent');
            const toggleIcon = document.getElementById('toggleIcon');

            toggleHeader.addEventListener('click', function() {
                formContent.classList.toggle('expanded');
                toggleIcon.classList.toggle('rotate-180');
            });
        }

        // Add form initialization
        function initializeAddForm() {
            const uploadZone = document.querySelector('#addFormContent .upload-zone');
            const imageInput = document.getElementById('addImageInput');
            
            // Upload zone click handler
            uploadZone.addEventListener('click', () => imageInput.click());
            
            // File input change handler
            imageInput.addEventListener('change', (e) => handleFileSelection(e, 'add'));
            
            // Drag and drop handlers
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', (e) => handleFileDrop(e, 'add'));
        }

        // Edit form initialization
        function initializeEditForm() {
            const uploadZone = document.querySelector('#editModal .upload-zone');
            const imageInput = document.getElementById('editImageInput');
            
            // Upload zone click handler
            uploadZone.addEventListener('click', () => imageInput.click());
            
            // File input change handler
            imageInput.addEventListener('change', (e) => handleFileSelection(e, 'edit'));
            
            // Drag and drop handlers
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', (e) => handleFileDrop(e, 'edit'));
        }

        // File selection handler
        function handleFileSelection(event, context) {
            const files = Array.from(event.target.files);
            processFiles(files, context);
        }

        // Drag and drop handlers
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(event, context) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            processFiles(files, context);
        }

        // Process selected files with enhanced compression
        function processFiles(files, context) {
            // Filter image files
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length === 0) {
                alert('Please select valid image files.');
                return;
            }

            if (context === 'add') {
                // Append new files to existing ones instead of replacing
                addSelectedFiles = [...addSelectedFiles, ...imageFiles];
                console.log('Total files after adding:', addSelectedFiles.length);
                compressAndPreviewFiles(addSelectedFiles, 'add');
            } else {
                // For edit context, append new files to existing ones
                editSelectedFiles = [...editSelectedFiles, ...imageFiles];
                console.log('Total files after adding:', editSelectedFiles.length);
                compressAndPreviewFiles(editSelectedFiles, 'edit');
            }
        }

        // Enhanced compression and preview with guaranteed 5MB max
        async function compressAndPreviewFiles(files, context) {
            const progressContainer = document.getElementById(context + 'CompressionProgress');
            const progressBar = document.getElementById(context + 'ProgressBar');
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');
            
            progressContainer.classList.remove('hidden');
            previewGrid.innerHTML = '';
            
            const compressedFiles = [];
            const maxSizeBytes = 5 * 1024 * 1024; // 5MB
            
            try {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    progressBar.style.width = `${((i + 1) / files.length) * 100}%`;

                    try {
                        // Compress with multiple quality levels until under 5MB
                        let compressedFile = await compressImageToSize(file, maxSizeBytes);

                        if (compressedFile) {
                            compressedFiles.push({
                                original: file,
                                compressed: compressedFile,
                                index: i
                            });

                            createImagePreview(compressedFile, file, i, context);
                        } else {
                            throw new Error('Compression returned null');
                        }
                    } catch (error) {
                        console.error('Error compressing image:', error);

                        // If compression fails, still try to use original if under 5MB
                        if (file.size <= maxSizeBytes) {
                            compressedFiles.push({
                                original: file,
                                compressed: file,
                                index: i
                            });
                            createImagePreview(file, file, i, context);
                            console.log(`Using original file for ${file.name} (compression failed but file is under size limit)`);
                        } else {
                            const errorMsg = error.message.includes('timeout')
                                ? `Image compression timed out for ${file.name}. Please try a smaller image or different format.`
                                : `Failed to compress ${file.name} to required size (${formatFileSize(maxSizeBytes)}). Current size: ${formatFileSize(file.size)}. Please choose a smaller image.`;

                            alert(errorMsg);
                            console.error(`Compression failed for ${file.name}:`, error);
                        }
                    }
                }
            } finally {
                // Always hide progress bar when done, even if there's an error
                console.log('Finally block reached - hiding progress container for context:', context);

                setTimeout(() => {
                    if (progressContainer) {
                        progressContainer.classList.add('hidden');
                        console.log('Progress container successfully hidden for context:', context);
                    } else {
                        console.error('Progress container not found when trying to hide for context:', context);
                    }
                }, 500);
            }
            updateUploadSummary(compressedFiles, context);
            
            // Update form data
            updateFormData(compressedFiles, context);
        }

        // Optimized compression function for better performance
        async function compressImageToSize(file, maxSizeBytes) {
            try {
                // Start with reasonable dimensions based on file size
                let maxDimension = file.size > 10 * 1024 * 1024 ? 800 : 1200; // 10MB threshold
                let quality = 0.8; // Start with lower quality for faster processing
                let compressedFile;
                let attempts = 0;
                const maxAttempts = 4; // Limit attempts to prevent infinite loops

                // Quick size check - if already small enough, use higher quality
                if (file.size <= maxSizeBytes) {
                    return await compressImage(file, maxDimension, maxDimension, 0.9);
                }

                // Progressive compression with limited attempts
                do {
                    try {
                        compressedFile = await compressImage(file, maxDimension, maxDimension, quality);
                        attempts++;

                        if (compressedFile && compressedFile.size <= maxSizeBytes) {
                            break;
                        }

                        // Adjust parameters more aggressively for faster convergence
                        if (attempts === 1) {
                            quality = 0.6;
                        } else if (attempts === 2) {
                            quality = 0.4;
                            maxDimension = Math.floor(maxDimension * 0.8);
                        } else {
                            maxDimension = Math.floor(maxDimension * 0.7);
                            quality = 0.3;
                        }
                    } catch (error) {
                        console.error(`Compression attempt ${attempts + 1} failed:`, error);
                        // If compression fails, try with more aggressive settings
                        quality = Math.max(0.1, quality - 0.2);
                        maxDimension = Math.floor(maxDimension * 0.8);
                        attempts++;
                    }

                } while ((!compressedFile || compressedFile.size > maxSizeBytes) && attempts < maxAttempts);

                if (!compressedFile) {
                    throw new Error('Failed to compress image after all attempts');
                }

                return compressedFile;
            } catch (error) {
                console.error('Image compression failed:', error);
                throw error;
            }
        }

        // Image compression function with timeout and error handling
        function compressImage(file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                let blobUrl = null;

                // Set timeout to prevent hanging
                const timeout = setTimeout(() => {
                    if (blobUrl) URL.revokeObjectURL(blobUrl);
                    reject(new Error('Image compression timeout'));
                }, 30000); // 30 second timeout

                img.onload = () => {
                    try {
                        let { width, height } = calculateDimensions(
                            img.width,
                            img.height,
                            maxWidth,
                            maxHeight
                        );

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);
                        canvas.toBlob((blob) => {
                            clearTimeout(timeout);
                            if (blobUrl) URL.revokeObjectURL(blobUrl);

                            if (blob) {
                                resolve(blob);
                            } else {
                                reject(new Error('Failed to create compressed blob'));
                            }
                        }, 'image/jpeg', quality);
                    } catch (error) {
                        clearTimeout(timeout);
                        if (blobUrl) URL.revokeObjectURL(blobUrl);
                        reject(error);
                    }
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    if (blobUrl) URL.revokeObjectURL(blobUrl);
                    reject(new Error('Failed to load image for compression'));
                };

                try {
                    blobUrl = URL.createObjectURL(file);
                    img.src = blobUrl;
                } catch (error) {
                    clearTimeout(timeout);
                    reject(error);
                }
            });
        }

        // Calculate new dimensions maintaining aspect ratio
        function calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
            const ratio = Math.min(maxWidth / originalWidth, maxHeight / originalHeight);
            
            if (ratio >= 1) {
                return { width: originalWidth, height: originalHeight };
            }
            
            return {
                width: Math.round(originalWidth * ratio),
                height: Math.round(originalHeight * ratio)
            };
        }

        // Create image preview
        function createImagePreview(compressedFile, originalFile, index, context) {
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');
            const previewDiv = document.createElement('div');
            previewDiv.className = 'image-preview relative';
            previewDiv.dataset.index = index;
            
            const img = document.createElement('img');
            img.src = URL.createObjectURL(compressedFile);
            img.alt = `Preview ${index + 1}`;
            
            const overlay = document.createElement('div');
            overlay.className = 'image-overlay';
            
            const actions = document.createElement('div');
            actions.className = 'flex space-x-2';
            
            // Set as display image button
            const displayBtn = document.createElement('button');
            displayBtn.type = 'button';
            displayBtn.className = 'bg-orange-500 text-white px-2 py-1 rounded text-xs hover:bg-orange-600 transition-colors';
            displayBtn.innerHTML = '<i class="fas fa-star"></i>';
            displayBtn.title = 'Set as display image';
            displayBtn.onclick = () => setDisplayImage(index, context);
            
            // Delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = 'Remove image';
            deleteBtn.onclick = () => removeImage(index, context);
            
            actions.appendChild(displayBtn);
            actions.appendChild(deleteBtn);
            overlay.appendChild(actions);
            
            // Compression info
            const info = document.createElement('div');
            info.className = 'compression-info mt-1 text-center';
            const originalSize = formatFileSize(originalFile.size);
            const compressedSize = formatFileSize(compressedFile.size);
            const savings = Math.round((1 - compressedFile.size / originalFile.size) * 100);
            info.innerHTML = `${originalSize} → ${compressedSize} (${savings}% saved)`;
            
            previewDiv.appendChild(img);
            previewDiv.appendChild(overlay);
            previewDiv.appendChild(info);
            
            // Display image indicator
            const currentDisplayIndex = context === 'add' ? addDisplayImageIndex : editDisplayImageIndex;
            if (index === currentDisplayIndex) {
                const indicator = document.createElement('div');
                indicator.className = 'display-image-indicator';
                indicator.textContent = 'Display';
                previewDiv.appendChild(indicator);
            }
            
            previewGrid.appendChild(previewDiv);
        }

        // Set display image
        function setDisplayImage(index, context) {
            if (context === 'add') {
                addDisplayImageIndex = index;
                document.getElementById('addDisplayImageIndex').value = index;
            } else {
                editDisplayImageIndex = index;
                document.getElementById('editDisplayImageIndex').value = index;
            }
            
            // Update visual indicators
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');
            previewGrid.querySelectorAll('.display-image-indicator').forEach(el => el.remove());
            previewGrid.querySelectorAll('.image-preview').forEach((el, i) => {
                if (i === index) {
                    const indicator = document.createElement('div');
                    indicator.className = 'display-image-indicator';
                    indicator.textContent = 'Display';
                    el.appendChild(indicator);
                }
            });
        }

        // Remove image from selection
        function removeImage(index, context) {
            if (context === 'add') {
                addSelectedFiles.splice(index, 1);
                
                // Adjust display image index
                if (addDisplayImageIndex === index) {
                    addDisplayImageIndex = -1;
                    document.getElementById('addDisplayImageIndex').value = '';
                } else if (addDisplayImageIndex > index) {
                    addDisplayImageIndex--;
                    document.getElementById('addDisplayImageIndex').value = addDisplayImageIndex;
                }
                
                // Re-render previews
                if (addSelectedFiles.length > 0) {
                    compressAndPreviewFiles(addSelectedFiles, 'add');
                } else {
                    document.getElementById('addImagePreviewGrid').innerHTML = '';
                    document.getElementById('addUploadSummary').classList.add('hidden');
                }
            } else {
                editSelectedFiles.splice(index, 1);
                
                // Adjust display image index
                if (editDisplayImageIndex === index) {
                    editDisplayImageIndex = -1;
                    document.getElementById('editDisplayImageIndex').value = '';
                } else if (editDisplayImageIndex > index) {
                    editDisplayImageIndex--;
                    document.getElementById('editDisplayImageIndex').value = editDisplayImageIndex;
                }
                
                // Re-render previews
                if (editSelectedFiles.length > 0) {
                    compressAndPreviewFiles(editSelectedFiles, 'edit');
                } else {
                    document.getElementById('editImagePreviewGrid').innerHTML = '';
                    document.getElementById('editUploadSummary').classList.add('hidden');
                }
            }
        }

        // Update upload summary
        function updateUploadSummary(compressedFiles, context) {
            const summaryContainer = document.getElementById(context + 'UploadSummary');
            const totalFiles = document.getElementById(context + 'TotalFiles');
            const totalSize = document.getElementById(context + 'TotalSize');
            
            const totalSizeBytes = compressedFiles.reduce((sum, file) => sum + file.compressed.size, 0);
            
            totalFiles.textContent = compressedFiles.length;
            totalSize.textContent = formatFileSize(totalSizeBytes);
            
            summaryContainer.classList.remove('hidden');
        }

        // Update form data with compressed files
        function updateFormData(compressedFiles, context) {
            const dataTransfer = new DataTransfer();
            compressedFiles.forEach(fileData => {
                dataTransfer.items.add(fileData.compressed);
            });
            
            if (context === 'add') {
                document.getElementById('addImageInput').files = dataTransfer.files;
            } else {
                document.getElementById('editImageInput').files = dataTransfer.files;
            }
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Reset add form
        function resetAddForm() {
            document.getElementById('addDestinationForm').reset();
            addSelectedFiles = [];
            addDisplayImageIndex = -1;
            document.getElementById('addImagePreviewGrid').innerHTML = '';
            document.getElementById('addUploadSummary').classList.add('hidden');
            document.getElementById('addDisplayImageIndex').value = '';
        }

        // Edit destination function
        function editDestination(id) {
            // Find destination data from the loaded destinations array
            const destination = destinations.find(d => d.destination_id == id);

            if (destination) {
                // Populate form fields
                document.getElementById('editDestinationId').value = destination.destination_id;
                document.getElementById('editDestinationName').value = destination.name || '';
                document.getElementById('editDestinationLocation').value = destination.location || '';
                document.getElementById('editDestinationPrice').value = destination.price || '';
                document.getElementById('editShortDescription').value = destination.short_description || '';


                // Reset new image upload state
                editSelectedFiles = [];
                editDisplayImageIndex = -1;
                document.getElementById('editImagePreviewGrid').innerHTML = '';
                document.getElementById('editUploadSummary').classList.add('hidden');

                // Load existing images
                loadExistingImages(id);

                // Show the modal
                document.getElementById('editModal').classList.remove('hidden');
            } else {
                alert('Error: Destination not found');
            }
        }

        // Load existing images for editing
        function loadExistingImages(destinationId) {
            fetch(`api/images.php/destinations/${destinationId}/images`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        existingImages = result.data;
                        displayExistingImages(result.data);
                    }
                })
                .catch(error => {
                    console.error('Error loading existing images:', error);
                });
        }

        // Display existing images
        function displayExistingImages(images) {
            const container = document.getElementById('existingImagesContainer');
            const grid = document.getElementById('existingImagesGrid');

            if (images.length === 0) {
                container.classList.add('hidden');
                return;
            }

            container.classList.remove('hidden');
            grid.innerHTML = '';

            images.forEach(image => {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'relative group';
                previewDiv.setAttribute('data-image-id', image.image_id);

                const img = document.createElement('img');
                img.src = image.url;
                img.alt = image.alt_text || 'Destination image';
                img.className = 'w-full h-24 object-cover rounded-lg';

                const overlay = document.createElement('div');
                overlay.className = 'absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center';
                
                const actions = document.createElement('div');
                actions.className = 'flex space-x-2';
                
                // Set as display image button
                const displayBtn = document.createElement('button');
                displayBtn.type = 'button';
                displayBtn.className = 'bg-orange-500 text-white px-2 py-1 rounded text-xs hover:bg-orange-600 transition-colors';
                displayBtn.innerHTML = '<i class="fas fa-star"></i>';
                displayBtn.title = 'Set as display image';
                displayBtn.onclick = () => setExistingDisplayImage(image.image_id);
                
                // Delete button
                const deleteBtn = document.createElement('button');
                deleteBtn.type = 'button';
                deleteBtn.className = 'bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors';
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                deleteBtn.title = 'Delete image';
                deleteBtn.onclick = () => deleteExistingImage(image.image_id, previewDiv);
                
                actions.appendChild(displayBtn);
                actions.appendChild(deleteBtn);
                overlay.appendChild(actions);
                
                previewDiv.appendChild(img);
                previewDiv.appendChild(overlay);
                
                // Display image indicator
                if (image.is_display_image) {
                    const indicator = document.createElement('div');
                    indicator.className = 'display-image-indicator';
                    indicator.textContent = 'Display';
                    previewDiv.appendChild(indicator);
                }
                
                grid.appendChild(previewDiv);
            });
        }

        // Set existing image as display image
        function setExistingDisplayImage(imageId) {
            const destinationId = document.getElementById('editDestinationId').value;

            fetch('destinations.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=set_display_image&image_id=${imageId}&destination_id=${destinationId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update visual indicators
                    document.querySelectorAll('#existingImagesGrid .display-image-indicator').forEach(el => el.remove());
                    const imageElement = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (imageElement) {
                        const indicator = document.createElement('div');
                        indicator.className = 'display-image-indicator';
                        indicator.textContent = 'Display';
                        imageElement.appendChild(indicator);
                    }
                    alert('Display image updated successfully!');
                } else {
                    alert('Failed to set display image: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error setting display image');
            });
        }

        // Delete existing image
        function deleteExistingImage(imageId, previewElement) {
            if (!confirm('Are you sure you want to delete this image?')) {
                return;
            }
            
            const destinationId = document.getElementById('editDestinationId').value;
            
            fetch('destinations.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=delete_image&image_id=${imageId}&destination_id=${destinationId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    previewElement.remove();
                    existingImages = existingImages.filter(img => img.image_id !== imageId);
                    
                    // Hide container if no images left
                    if (existingImages.length === 0) {
                        document.getElementById('existingImagesContainer').classList.add('hidden');
                    }
                } else {
                    alert('Error deleting image: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error deleting image');
                console.error('Error:', error);
            });
        }
        
        function deleteDestination(id) {
            if (confirm('Are you sure you want to delete this destination? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
            
            // Clean up object URLs to prevent memory leaks
            document.querySelectorAll('#editImagePreviewGrid img').forEach(img => {
                if (img.src.startsWith('blob:')) {
                    URL.revokeObjectURL(img.src);
                }
            });
        }
    </script>
</body>
</html>

