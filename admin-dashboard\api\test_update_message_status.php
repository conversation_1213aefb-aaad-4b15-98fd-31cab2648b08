<?php
// Test version without authentication
header('Content-Type: application/json');

require_once '../config/config.php';
require_once '../classes/models.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

if (!isset($input['message_id']) || !is_numeric($input['message_id'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid message ID']);
    exit;
}

if (!isset($input['action']) || !in_array($input['action'], ['mark_read', 'mark_unread'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid action']);
    exit;
}

$messageId = intval($input['message_id']);
$action = $input['action'];
$messageModel = new Message();

try {
    if ($action === 'mark_read') {
        $success = $messageModel->markAsRead($messageId);
        $newStatus = 1; // read
    } else {
        // Mark as unread
        $sql = "UPDATE messages SET is_read = 0 WHERE message_id = :id";
        $stmt = $messageModel->getDb()->prepare($sql);
        $stmt->bindParam(':id', $messageId);
        $success = $stmt->execute();
        $newStatus = 0; // unread
    }
    
    if ($success) {
        // Get updated unread count
        $stats = $messageModel->getStats();
        
        echo json_encode([
            'success' => true,
            'message' => 'Message status updated successfully',
            'new_status' => $newStatus,
            'unread_count' => $stats['unread']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to update message status'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
