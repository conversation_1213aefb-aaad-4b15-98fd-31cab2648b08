<?php
/**
 * Email Fetch Cron Job
 * This script should be run via cron job every 5-10 minutes
 * 
 * Cron job example (every 5 minutes):
 * */5 * * * * /usr/bin/php /path/to/your/site/admin-dashboard/cron/email-fetch-cron.php
 * 
 * Or via wget/curl (every 5 minutes):
 * */5 * * * * wget -q -O /dev/null "https://yourdomain.com/admin-dashboard/cron/email-fetch-cron.php?cron_key=meleva_email_cron_2024"
 */

// Prevent browser access without proper key
if (php_sapi_name() !== 'cli') {
    if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== 'meleva_email_cron_2024') {
        http_response_code(403);
        die('Access denied');
    }
    header('Content-Type: text/plain');
}

// Set execution limits
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '256M');

// Change to the admin dashboard directory
chdir(dirname(__DIR__));

require_once 'config/config.php';
require_once 'classes/ImapEmailFetcher.php';

// Lock file to prevent multiple instances
$lockFile = sys_get_temp_dir() . '/meleva_email_fetch.lock';

if (file_exists($lockFile)) {
    $lockTime = filemtime($lockFile);
    // If lock file is older than 10 minutes, remove it (stale lock)
    if (time() - $lockTime > 600) {
        unlink($lockFile);
    } else {
        error_log("Email fetch cron: Another instance is already running");
        exit(0);
    }
}

// Create lock file
file_put_contents($lockFile, getmypid());

try {
    $startTime = microtime(true);
    
    $fetcher = new ImapEmailFetcher();
    $results = $fetcher->fetchAllEmails();
    
    $totalProcessed = 0;
    $totalErrors = 0;
    $accountResults = [];
    
    foreach ($results as $email => $result) {
        if (isset($result['error'])) {
            $totalErrors++;
            $accountResults[] = "$email: ERROR - " . $result['error'];
            error_log("Email fetch error for $email: " . $result['error']);
        } else {
            $totalProcessed += $result['processed'];
            $accountResults[] = "$email: {$result['processed']}/{$result['total_found']} processed";
            
            if (!empty($result['errors'])) {
                $totalErrors += count($result['errors']);
                foreach ($result['errors'] as $error) {
                    error_log("Email processing error for $email: $error");
                }
            }
        }
    }
    
    $executionTime = round(microtime(true) - $startTime, 2);
    
    // Log summary
    $logMessage = "Email fetch cron completed: {$totalProcessed} emails processed, {$totalErrors} errors, {$executionTime}s execution time";
    error_log($logMessage);
    
    // Detailed logging if there were emails processed
    if ($totalProcessed > 0) {
        error_log("Email fetch details: " . implode(', ', $accountResults));
    }
    
    // Output for web-based cron (if accessed via URL)
    if (php_sapi_name() !== 'cli') {
        echo "Email fetch completed successfully\n";
        echo "Processed: $totalProcessed emails\n";
        echo "Errors: $totalErrors\n";
        echo "Execution time: {$executionTime}s\n";
        echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
    }
    
} catch (Exception $e) {
    $errorMsg = "Email fetch cron failed: " . $e->getMessage();
    error_log($errorMsg);
    
    if (php_sapi_name() !== 'cli') {
        http_response_code(500);
        echo "ERROR: $errorMsg\n";
    }
} finally {
    // Remove lock file
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
}

// Update last run timestamp
$lastRunFile = dirname(__DIR__) . '/cache/last_email_fetch.txt';
$cacheDir = dirname($lastRunFile);
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0755, true);
}
file_put_contents($lastRunFile, date('Y-m-d H:i:s'));
?>
