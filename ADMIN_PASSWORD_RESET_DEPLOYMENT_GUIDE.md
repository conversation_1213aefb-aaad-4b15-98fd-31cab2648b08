# Admin Password Reset - Live Server Deployment Guide

## Overview
This guide ensures the admin password reset functionality works correctly on the live server.

## Pre-Deployment Checklist

### 1. Email Account Verification
Before deploying, verify these email accounts exist and are properly configured:

**Required Email Accounts:**
- `<EMAIL>` (Primary account for admin notifications)
- `<EMAIL>` (For quote-related emails)

**Email Account Settings:**
- **SMTP Host:** `melevatours.co.ke`
- **SMTP Port:** `465` (SSL)
- **Encryption:** SSL/TLS
- **Password:** `hi$Ch9=lYcap{7cA`

### 2. Database Configuration
Update `admin-dashboard/config/config.php` with live server credentials:

```php
class DatabaseConfig {
    const DB_HOST = 'localhost'; // Or your hosting provider's DB host
    const DB_NAME = 'your_live_database_name';
    const DB_USER = 'your_live_database_user';
    const DB_PASS = 'your_live_database_password';
    // ... rest of configuration
}
```

### 3. SSL Certificate
Ensure your live server has a valid SSL certificate installed for:
- `https://melevatours.co.ke`
- This is important for secure password reset links

## Deployment Steps

### Step 1: Upload Files
Upload all admin dashboard files to your live server, maintaining the directory structure.

### Step 2: Database Setup
1. Import the database schema using `admin-dashboard/database.sql`
2. Ensure the `password_resets` table exists with proper structure
3. Verify the admin user account exists

### Step 3: Test Email Configuration
Run this test to verify email functionality:

```php
// Create a test file: test-email.php
<?php
require_once 'admin-dashboard/config/config.php';
use PHPMailer\PHPMailer\PHPMailer;
require 'admin-dashboard/vendor/autoload.php';

$mail = new PHPMailer(true);
try {
    $mail->isSMTP();
    $mail->Host = 'melevatours.co.ke';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'hi$Ch9=lYcap{7cA';
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = 465;
    
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );
    
    $mail->setFrom('<EMAIL>', 'Test');
    $mail->addAddress('<EMAIL>');
    $mail->Subject = 'SMTP Test';
    $mail->Body = 'SMTP configuration test successful!';
    
    $mail->send();
    echo 'Email sent successfully!';
} catch (Exception $e) {
    echo 'Email failed: ' . $e->getMessage();
}
?>
```

### Step 4: Test Password Reset Flow
1. Navigate to `https://yourdomain.com/admin-dashboard/forgot-password.php`
2. Enter a valid admin email address
3. Check that the email is received
4. Click the reset link and verify it works
5. Complete the password reset process

## Troubleshooting

### Common Issues and Solutions

#### 1. Email Not Sending
**Symptoms:** No password reset email received
**Solutions:**
- Verify email account credentials are correct
- Check server error logs for SMTP errors
- Ensure firewall allows outbound connections on port 465
- Test SMTP connection using the test script above

#### 2. Reset Link Not Working
**Symptoms:** "Invalid or expired token" error
**Solutions:**
- Check database connection
- Verify `password_resets` table exists
- Ensure server time is correct (affects token expiration)
- Check if token was already used

#### 3. SSL/HTTPS Issues
**Symptoms:** Mixed content warnings or insecure links
**Solutions:**
- Ensure SSL certificate is properly installed
- Update any hardcoded HTTP links to HTTPS
- Check .htaccess for HTTPS redirects

#### 4. Database Connection Issues
**Symptoms:** Database connection errors
**Solutions:**
- Verify database credentials in config.php
- Ensure database exists and is accessible
- Check database user permissions
- Verify database server is running

## Security Features

### Built-in Security Measures
1. **User Validation:** Only registered admin users can request password resets
2. **Role Verification:** Only users with 'admin' or 'super_admin' roles are allowed
3. **Rate Limiting:** Maximum 3 attempts per IP address per hour
4. **Clear Error Messages:** Non-existent users get explicit error messages
5. **Security Logging:** All attempts are logged for monitoring
6. **Token Security:** 1-hour expiration with single-use tokens

### Production Security Settings
1. **Error Reporting:** Disable in production
   ```php
   ini_set('display_errors', 0);
   error_reporting(0);
   ```

2. **HTTPS Only:** Force HTTPS for admin pages
3. **Strong Passwords:** Ensure admin accounts use strong passwords
4. **Regular Updates:** Keep PHPMailer and other dependencies updated

### Monitoring
Set up monitoring for:
- Failed password reset attempts
- Email delivery failures
- Database connection issues
- Unusual admin login patterns

## Testing Checklist

After deployment, test these scenarios:

**Valid Admin User Tests:**
- [ ] Admin can request password reset
- [ ] Password reset email is received
- [ ] Reset link works and is secure (HTTPS)
- [ ] Password can be successfully changed
- [ ] Old password no longer works
- [ ] Token expires after 1 hour
- [ ] Used tokens cannot be reused
- [ ] Invalid tokens show appropriate error

**Security Tests:**
- [ ] Non-existent email shows clear error message
- [ ] Rate limiting works (max 3 attempts per hour)
- [ ] Only admin/super_admin roles can request resets
- [ ] Proper error logging for security events
- [ ] No sensitive information leaked in error messages

## Support

If you encounter issues:
1. Check server error logs first
2. Enable SMTP debugging temporarily (set SMTPDebug = 2)
3. Verify all email accounts are active and not locked
4. Test database connectivity separately
5. Ensure all file permissions are correct

## File Permissions
Set these permissions on the live server:
- Config files: 644
- PHP files: 644
- Directories: 755
- Uploads directory: 755 (or 775 if needed)

Remember to remove any test files (like test-email.php) after deployment!
