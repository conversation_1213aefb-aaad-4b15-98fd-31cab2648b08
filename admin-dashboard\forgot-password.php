<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';

// Include PHPMailer classes
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use <PERSON><PERSON>Mailer\PHPMailer\Exception;

// Load Composer's autoloader
require 'vendor/autoload.php';

// Check if user is already logged in
Auth::startSession();
if (Auth::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$message = '';
$messageType = '';

// Simple rate limiting - prevent too many requests from same IP
$userIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$rateLimitKey = 'password_reset_' . md5($userIP);
$maxAttempts = 3; // Maximum attempts per hour
$timeWindow = 3600; // 1 hour

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check rate limiting
    $attempts = $_SESSION[$rateLimitKey] ?? ['count' => 0, 'first_attempt' => time()];

    // Reset counter if time window has passed
    if (time() - $attempts['first_attempt'] > $timeWindow) {
        $attempts = ['count' => 0, 'first_attempt' => time()];
    }

    if ($attempts['count'] >= $maxAttempts) {
        $message = 'Too many password reset attempts. Please wait an hour before trying again.';
        $messageType = 'error';
        error_log("Rate limit exceeded for password reset from IP: " . $userIP);
    } else {
    $email = Utils::sanitizeInput($_POST['email']);
    
    if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $userModel = new User();
        $user = $userModel->getUserByEmail($email);
        
        if ($user) {
            // Verify user is an admin (additional security check)
            if (in_array($user['role'], ['admin', 'super_admin'])) {
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));

                // Save reset token to database
                if ($userModel->createPasswordResetToken($user['user_id'], $token, $expiresAt)) {
                    // Send email with reset link - Smart URL detection for localhost vs live server
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                    $resetLink = $protocol . "://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/reset-password.php?token=" . $token;

                    if (sendPasswordResetEmail($email, $user['username'], $resetLink)) {
                        $message = 'Password reset instructions have been sent to your email address.';
                        $messageType = 'success';
                        error_log("Password reset email sent successfully to admin user: " . $email);
                    } else {
                        $message = 'Failed to send email. Please try again later.';
                        $messageType = 'error';
                    }
                } else {
                    $message = 'Failed to generate reset token. Please try again.';
                    $messageType = 'error';
                }
            } else {
                // User exists but is not an admin
                $message = 'Access denied. Password reset is only available for admin accounts.';
                $messageType = 'error';
                error_log("Password reset attempted by non-admin user: " . $email . " (Role: " . $user['role'] . ")");
            }
        } else {
            // Show clear error message for non-existent admin users
            $message = 'No admin account found with that email address. Please check your email and try again, or contact the system administrator.';
            $messageType = 'error';
            error_log("Password reset attempted for non-existent email: " . $email);
        }

        // Update rate limiting counter
        $attempts['count']++;
        $_SESSION[$rateLimitKey] = $attempts;

    } else {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    }
    } // Close rate limiting check
}

// Email sending function using PHPMailer
function sendPasswordResetEmail($email, $username, $resetLink) {
    $mail = new PHPMailer(true);

    try {
        // Server settings - Using Meleva Tours SMTP configuration
        $mail->isSMTP();                                            // Send using SMTP
        $mail->Host       = 'melevatours.co.ke';                   // Set the SMTP server to send through
        $mail->SMTPAuth   = true;                                   // Enable SMTP authentication
        $mail->Username   = '<EMAIL>';              // SMTP username (using configured account)
        $mail->Password   = 'hi$Ch9=lYcap{7cA';                    // SMTP password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;           // Enable SSL encryption
        $mail->Port       = 465;                                    // TCP port for SSL

        // Additional settings for cPanel compatibility
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Enable debugging for troubleshooting (set to 0 for production)
        $mail->SMTPDebug = 0; // Set to 2 for debugging if needed
        $mail->Debugoutput = 'error_log';

        // Recipients
        $mail->setFrom('<EMAIL>', 'Meleva Tours and Travel Admin System');
        $mail->addAddress($email, $username);     // Add a recipient
        $mail->addReplyTo('<EMAIL>', 'Meleva Tours and Travel Admin System');

        // Content
        $mail->isHTML(true);                                  // Set email format to HTML
        $mail->Subject = 'Admin Password Reset - Meleva Tours and Travel';
        $mail->Body    = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Admin Password Reset</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .logo { max-width: 100px; height: auto; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 10px; margin: 20px 0; }
                .button { display: inline-block; background-color: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class='header'>
                <img src='https://melevatours.co.ke/images/meleva-lg.png' alt='Meleva Tours and Travel' class='logo'>
                <h1 style='color: #f97316; margin: 10px 0;'>Admin Password Reset</h1>
            </div>

            <div class='content'>
                <p><strong>Hello $username,</strong></p>
                <p>You have requested to reset your password for your Meleva Tours and Travel admin account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center;'>
                    <a href='$resetLink' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 3px;'>$resetLink</p>

                <div class='warning'>
                    <strong>Important:</strong>
                    <ul>
                        <li>This link will expire in 1 hour for security reasons</li>
                        <li>If you did not request this password reset, please ignore this email</li>
                        <li>For security, do not share this link with anyone</li>
                    </ul>
                </div>
            </div>

            <div class='footer'>
                <p><strong>Smooth Travels, Seamless Experiences!</strong></p>
                <p>Meleva Tours and Travel<br>
                Email: <EMAIL> | Website: www.melevatours.co.ke</p>
                <p style='margin-top: 20px;'>© 2025 Meleva Tours and Travel. All rights reserved.</p>
            </div>
        </body>
        </html>
        ";
        $mail->AltBody = "Admin Password Reset - Meleva Tours and Travel\n\nHello $username,\n\nYou have requested to reset your password for your Meleva Tours and Travel admin account.\n\nPlease copy and paste this link into your browser to reset your password:\n$resetLink\n\nThis link will expire in 1 hour for security reasons.\n\nIf you did not request this password reset, please ignore this email.\n\nSmooth Travels, Seamless Experiences!\nMeleva Tours and Travel\nEmail: <EMAIL>";

        $mail->send();
        error_log("Password reset email sent successfully to: " . $email);
        return true;
    } catch (Exception $e) {
        error_log("Password reset email failed. Mailer Error: {$mail->ErrorInfo}. Exception: " . $e->getMessage());
        return false;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .safari-pattern {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen safari-pattern">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo and Title -->
            <div class="text-center">
                <div class="mx-auto w-20 h-20 lg:w-40 lg:h-40 flex items-center justify-center mb-4">
                    <img src="images/meleva-lg.png" alt="logo">
                </div>
                <h2 class="text-3xl font-bold text-gray-900">Forgot Password</h2>
                <p class="mt-2 text-lg text-gray-600">Enter your admin email to reset your password</p>
                <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-1"></i>
                        Password reset is only available for registered admin accounts.
                    </p>
                </div>
            </div>
            
            <!-- Forgot Password Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <?php if ($message): ?>
                    <div class="mb-4 <?php echo $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'; ?> border px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                            <span><?php echo htmlspecialchars($message); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                required 
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Enter your email address"
                                value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                            >
                        </div>
                    </div>
                    
                    <div>
                        <button 
                            type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                        >
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-paper-plane text-orange-300 group-hover:text-orange-200"></i>
                            </span>
                            Send Reset Instructions
                        </button>
                    </div>
                </form>
                
                <div class="mt-6 text-center">
                    <a href="login.php" class="text-sm text-orange-600 hover:text-orange-500 font-medium">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Login
                    </a>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    © 2025 Meleva Tours & Travel. All rights reserved.
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                e.preventDefault();
                alert('Please enter your email address');
                return;
            }
            
            // Basic email validation
            const emailRegex = /^[^
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address');
            }
        });
    </script>
</body>
</html>

