<?php
/**
 * IMAP Email Fetcher
 * Fetches emails from IMAP server and processes them for the admin system
 */

require_once 'models.php';
require_once 'AdminEmailHandler.php';

class ImapEmailFetcher {
    private $config;
    private $db;
    private $adminEmailHandler;
    
    public function __construct() {
        $this->config = include __DIR__ . '/../config/email-config.php';
        $this->db = Database::getInstance()->getConnection();
        $this->adminEmailHandler = new AdminEmailHandler();
    }
    
    /**
     * Fetch emails from all configured accounts
     */
    public function fetchAllEmails() {
        $results = [];
        
        foreach ($this->config['accounts'] as $account) {
            try {
                $result = $this->fetchEmailsFromAccount($account);
                $results[$account['email']] = $result;
                error_log("Fetched emails for {$account['email']}: " . json_encode($result));
            } catch (Exception $e) {
                error_log("Error fetching emails for {$account['email']}: " . $e->getMessage());
                $results[$account['email']] = ['error' => $e->getMessage()];
            }
        }
        
        return $results;
    }
    
    /**
     * Fetch emails from a specific account
     */
    private function fetchEmailsFromAccount($account) {
        // Build IMAP connection string
        $imapHost = '{' . $this->config['host'] . ':' . $this->config['port'] . '/imap/ssl}INBOX';
        
        // Connect to IMAP server
        $connection = imap_open($imapHost, $account['email'], $account['password']);
        
        if (!$connection) {
            throw new Exception('Failed to connect to IMAP server: ' . imap_last_error());
        }
        
        try {
            // Get unread emails
            $emails = imap_search($connection, 'UNSEEN');
            
            if (!$emails) {
                imap_close($connection);
                return ['processed' => 0, 'message' => 'No new emails'];
            }
            
            $processedCount = 0;
            $errors = [];
            
            foreach ($emails as $emailNumber) {
                try {
                    $emailData = $this->parseEmail($connection, $emailNumber);
                    
                    if ($emailData) {
                        // Process the email
                        $this->processIncomingEmail($emailData, $account);
                        
                        // Mark as read
                        imap_setflag_full($connection, $emailNumber, '\\Seen');
                        
                        $processedCount++;
                    }
                } catch (Exception $e) {
                    $errors[] = "Email #$emailNumber: " . $e->getMessage();
                    error_log("Error processing email #$emailNumber: " . $e->getMessage());
                }
            }
            
            imap_close($connection);
            
            return [
                'processed' => $processedCount,
                'total_found' => count($emails),
                'errors' => $errors
            ];
            
        } catch (Exception $e) {
            imap_close($connection);
            throw $e;
        }
    }
    
    /**
     * Parse email data from IMAP
     */
    private function parseEmail($connection, $emailNumber) {
        // Get email header
        $header = imap_headerinfo($connection, $emailNumber);
        
        if (!$header) {
            return null;
        }
        
        // Get email body
        $body = $this->getEmailBody($connection, $emailNumber);
        
        // Extract sender information
        $from = $header->from[0] ?? null;
        if (!$from) {
            return null;
        }
        
        $senderEmail = strtolower($from->mailbox . '@' . $from->host);
        $senderName = isset($from->personal) ? imap_mime_header_decode($from->personal)[0]->text : $senderEmail;
        
        // Get subject
        $subject = isset($header->subject) ? imap_mime_header_decode($header->subject)[0]->text : 'No Subject';
        
        // Get message ID
        $messageId = $header->message_id ?? 'generated_' . time() . '_' . $emailNumber;
        
        // Get In-Reply-To header
        $inReplyTo = $header->in_reply_to ?? null;
        
        return [
            'message_id' => $messageId,
            'in_reply_to' => $inReplyTo,
            'sender_email' => $senderEmail,
            'sender_name' => $senderName,
            'subject' => $subject,
            'content' => $body,
            'date' => date('Y-m-d H:i:s', $header->udate),
            'headers' => $this->extractCustomHeaders($connection, $emailNumber)
        ];
    }
    
    /**
     * Get email body content
     */
    private function getEmailBody($connection, $emailNumber) {
        $structure = imap_fetchstructure($connection, $emailNumber);
        
        if (!$structure) {
            return imap_body($connection, $emailNumber);
        }
        
        // Handle multipart messages
        if (isset($structure->parts) && count($structure->parts)) {
            $body = '';
            foreach ($structure->parts as $partNumber => $part) {
                $partBody = imap_fetchbody($connection, $emailNumber, $partNumber + 1);
                
                // Decode if needed
                if ($part->encoding == 3) { // Base64
                    $partBody = base64_decode($partBody);
                } elseif ($part->encoding == 4) { // Quoted-printable
                    $partBody = quoted_printable_decode($partBody);
                }
                
                // Prefer HTML content, fallback to plain text
                if ($part->subtype == 'HTML' || ($part->subtype == 'PLAIN' && empty($body))) {
                    $body = $partBody;
                }
            }
            return $body;
        } else {
            // Single part message
            $body = imap_body($connection, $emailNumber);
            
            // Decode if needed
            if ($structure->encoding == 3) { // Base64
                $body = base64_decode($body);
            } elseif ($structure->encoding == 4) { // Quoted-printable
                $body = quoted_printable_decode($body);
            }
            
            return $body;
        }
    }
    
    /**
     * Extract custom headers for conversation tracking
     */
    private function extractCustomHeaders($connection, $emailNumber) {
        $headers = imap_fetchheader($connection, $emailNumber);
        $customHeaders = [];
        
        // Look for conversation tracking headers
        if (preg_match('/X-Conversation-ID:\s*(.+)/i', $headers, $matches)) {
            $customHeaders['X-Conversation-ID'] = trim($matches[1]);
        }
        
        if (preg_match('/X-Original-To:\s*(.+)/i', $headers, $matches)) {
            $customHeaders['X-Original-To'] = trim($matches[1]);
        }
        
        return $customHeaders;
    }
    
    /**
     * Extract only the reply content, removing quoted original message
     */
    private function extractReplyContent($emailBody) {
        // Common patterns that indicate quoted content
        $quotedPatterns = [
            '/^On .* wrote:.*$/m',           // "On [date] [person] wrote:"
            '/^From:.*$/m',                  // Email headers in quoted content
            '/^Sent:.*$/m',                  // Outlook style headers
            '/^To:.*$/m',                    // To headers
            '/^Subject:.*$/m',               // Subject headers
            '/^Date:.*$/m',                  // Date headers
            '/^>.*$/m',                      // Lines starting with >
            '/^-----Original Message-----.*$/m', // Outlook original message
            '/^________________________________.*$/m', // Outlook separator
            '/^\s*From:.*\n.*\n.*\n.*$/m',   // Multi-line email headers
        ];

        $lines = explode("\n", $emailBody);
        $replyLines = [];
        $foundQuotedContent = false;

        foreach ($lines as $line) {
            $line = trim($line);

            // Check if this line indicates start of quoted content
            foreach ($quotedPatterns as $pattern) {
                if (preg_match($pattern, $line)) {
                    $foundQuotedContent = true;
                    break 2; // Break out of both loops
                }
            }

            // If we haven't found quoted content yet, this is part of the reply
            if (!$foundQuotedContent && !empty($line)) {
                $replyLines[] = $line;
            }
        }

        // Join the reply lines and clean up
        $replyContent = implode("\n", $replyLines);
        $replyContent = trim($replyContent);

        // If we couldn't extract a clean reply, return the first paragraph
        if (empty($replyContent) || strlen($replyContent) < 10) {
            $paragraphs = explode("\n\n", $emailBody);
            $replyContent = trim($paragraphs[0]);
        }

        return $replyContent;
    }

    /**
     * Process incoming email and save to database
     */
    private function processIncomingEmail($emailData, $account) {
        // Skip emails from admin addresses to avoid loops
        $adminEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        if (in_array($emailData['sender_email'], $adminEmails)) {
            error_log("Skipping email from admin address: " . $emailData['sender_email']);
            return;
        }
        
        // Determine message category based on account
        $messageCategory = $account['category'] ?? 'contact'; // 'contact' or 'quote'

        // Clean the message content to show only the reply (remove quoted original message)
        $cleanedContent = $this->extractReplyContent($emailData['content']);

        // Check if this is a reply to existing conversation
        $conversationId = $this->findOrCreateConversation($emailData, $messageCategory);

        // Save message to database
        $messageModel = new Message();
        $messageData = [
            'conversation_id' => $conversationId,
            'email_message_id' => $emailData['message_id'],
            'in_reply_to' => $emailData['in_reply_to'],
            'message_type' => 'incoming',
            'message_category' => $messageCategory,
            'sender_name' => $emailData['sender_name'],
            'sender_email' => $emailData['sender_email'],
            'subject' => $emailData['subject'],
            'message_content' => $cleanedContent,
            'email_status' => 'received',
            'is_read' => 0,
            'created_at' => $emailData['date']
        ];
        
        $messageId = $messageModel->create($messageData);
        
        if ($messageId) {
            error_log("Processed incoming email: {$emailData['subject']} from {$emailData['sender_email']}");
            
            // Update conversation status
            $this->updateConversationStatus($conversationId, 'open');
        } else {
            throw new Exception("Failed to save message to database");
        }
    }
    
    /**
     * Find existing conversation or create new one
     */
    private function findOrCreateConversation($emailData, $messageType) {
        // Try to find existing conversation
        if (!empty($emailData['in_reply_to'])) {
            $sql = "SELECT conversation_id FROM messages WHERE email_message_id = :message_id LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':message_id' => $emailData['in_reply_to']]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                return $result['conversation_id'];
            }
        }
        
        // Check for conversation ID in custom headers
        if (!empty($emailData['headers']['X-Conversation-ID'])) {
            return $emailData['headers']['X-Conversation-ID'];
        }
        
        // Create new conversation
        $conversationId = 'CONV_' . strtoupper(substr(md5(uniqid()), 0, 8));
        
        $sql = "INSERT INTO email_conversations (
            conversation_id, sender_name, sender_email, subject, 
            message_type, status, created_at, last_activity
        ) VALUES (
            :conversation_id, :sender_name, :sender_email, :subject,
            :message_type, 'open', NOW(), NOW()
        )";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':conversation_id' => $conversationId,
            ':sender_name' => $emailData['sender_name'],
            ':sender_email' => $emailData['sender_email'],
            ':subject' => $emailData['subject'],
            ':message_type' => $messageType
        ]);
        
        return $conversationId;
    }
    
    /**
     * Update conversation status
     */
    private function updateConversationStatus($conversationId, $status) {
        $sql = "UPDATE email_conversations SET status = :status, last_activity = NOW() WHERE conversation_id = :conversation_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':status' => $status,
            ':conversation_id' => $conversationId
        ]);
    }
}
?>
