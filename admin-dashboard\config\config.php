<?php
/**
 * Database Configuration
 */

// Set default timezone to East Africa Time (UTC+3)
date_default_timezone_set('Africa/Nairobi');

class DatabaseConfig {
    // Database connection parameters
    const DB_HOST = 'localhost';
    const DB_NAME = 'meleva_tours';
    const DB_USER = 'root';
    const DB_PASS = '';
    const DB_CHARSET = 'utf8mb4';

    // Timezone configuration
    const TIMEZONE = 'Africa/Nairobi'; // East Africa Time (UTC+3)

    // Application settings
    const UPLOAD_PATH = 'upload/images/';
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    // Session settings
    const SESSION_TIMEOUT = 3600; // 60 minutes
    const MAX_LOGIN_ATTEMPTS = 5; // Maximum login attempts before lockout
    const LOCKOUT_DURATION = 900; // 15 minutes lockout duration
    const CSRF_TOKEN_LIFETIME = 3600; // 1 hour for CSRF tokens

    // Pagination
    const ITEMS_PER_PAGE = 10;
}

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DatabaseConfig::DB_HOST .
                   ";dbname=" . DatabaseConfig::DB_NAME .
                   ";charset=" . DatabaseConfig::DB_CHARSET;

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->connection = new PDO($dsn, DatabaseConfig::DB_USER, DatabaseConfig::DB_PASS, $options);

            // Set MySQL timezone to match PHP timezone
            $this->connection->exec("SET time_zone = '+03:00'");
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
}

/**
 * Authentication Helper with Enhanced Security
 */
class Auth {
    public static function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            // Enhanced session security
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Lax');
            session_start();

            // Regenerate session ID periodically for security
            if (!isset($_SESSION['last_regeneration'])) {
                $_SESSION['last_regeneration'] = time();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        }
    }

    public static function isLoggedIn() {
        self::startSession();

        // Check basic session variables
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
            return false;
        }

        // Check session timeout
        if (self::isSessionExpired()) {
            self::logout();
            return false;
        }

        // Check for session hijacking
        if (!self::validateSession()) {
            self::logout();
            return false;
        }

        // Update last activity
        $_SESSION['last_activity'] = time();

        return true;
    }

    public static function requireLogin() {
        if (!self::isLoggedIn()) {
            // Clear any output buffer to prevent header issues
            if (ob_get_level()) {
                ob_end_clean();
            }

            // Store the attempted URL for redirect after login
            if (!isset($_SESSION['redirect_after_login']) && $_SERVER['REQUEST_METHOD'] === 'GET') {
                $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            }

            // Determine the correct path to login.php
            $loginPath = 'login.php';
            if (!file_exists($loginPath)) {
                $loginPath = dirname($_SERVER['PHP_SELF']) . '/login.php';
            }

            header('Location: ' . $loginPath);
            exit;
        }
    }
    
    public static function login($user) {
        self::startSession();

        // Clear any existing login attempts for this user
        self::clearLoginAttempts($user['username']);

        // Set session variables
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['user_ip'] = self::getUserIP();
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Generate new session ID for security
        session_regenerate_id(true);

        // Log successful login
        self::logSecurityEvent('login_success', $user['username']);
    }

    public static function logout() {
        self::startSession();

        // Log logout event
        if (isset($_SESSION['username'])) {
            self::logSecurityEvent('logout', $_SESSION['username']);
        }

        // Clear session data
        $_SESSION = array();

        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Destroy session
        session_destroy();
    }

    public static function getCurrentUser() {
        self::startSession();
        if (self::isLoggedIn()) {
            return [
                'user_id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email'],
                'role' => $_SESSION['role'],
                'login_time' => $_SESSION['login_time'] ?? null,
                'last_activity' => $_SESSION['last_activity'] ?? null
            ];
        }
        return null;
    }

    /**
     * Check if session has expired
     */
    public static function isSessionExpired() {
        if (!isset($_SESSION['last_activity'])) {
            return true;
        }

        return (time() - $_SESSION['last_activity']) > DatabaseConfig::SESSION_TIMEOUT;
    }

    /**
     * Validate session against hijacking
     */
    public static function validateSession() {
        // Check IP address consistency (optional - can cause issues with mobile users)
        // if (isset($_SESSION['user_ip']) && $_SESSION['user_ip'] !== self::getUserIP()) {
        //     return false;
        // }

        // Check user agent consistency (temporarily disabled for live server compatibility)
        // if (isset($_SESSION['user_agent']) && $_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
        //     return false;
        // }

        return true;
    }

    /**
     * Get user's IP address
     */
    public static function getUserIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Track login attempts
     */
    public static function recordLoginAttempt($username, $success = false) {
        self::startSession();

        if (!isset($_SESSION['login_attempts'])) {
            $_SESSION['login_attempts'] = [];
        }

        $key = $username . '_' . self::getUserIP();

        if (!isset($_SESSION['login_attempts'][$key])) {
            $_SESSION['login_attempts'][$key] = [
                'count' => 0,
                'last_attempt' => 0,
                'locked_until' => 0
            ];
        }

        if ($success) {
            // Clear attempts on successful login
            unset($_SESSION['login_attempts'][$key]);
        } else {
            // Increment failed attempts
            $_SESSION['login_attempts'][$key]['count']++;
            $_SESSION['login_attempts'][$key]['last_attempt'] = time();

            // Lock account if too many attempts
            if ($_SESSION['login_attempts'][$key]['count'] >= DatabaseConfig::MAX_LOGIN_ATTEMPTS) {
                $_SESSION['login_attempts'][$key]['locked_until'] = time() + DatabaseConfig::LOCKOUT_DURATION;
            }
        }

        // Log security event
        self::logSecurityEvent($success ? 'login_success' : 'login_failed', $username);
    }

    /**
     * Check if account is locked
     */
    public static function isAccountLocked($username) {
        self::startSession();

        if (!isset($_SESSION['login_attempts'])) {
            return false;
        }

        $key = $username . '_' . self::getUserIP();

        if (!isset($_SESSION['login_attempts'][$key])) {
            return false;
        }

        $attempts = $_SESSION['login_attempts'][$key];

        // Check if locked and lock period has expired
        if ($attempts['locked_until'] > 0) {
            if (time() > $attempts['locked_until']) {
                // Lock period expired, reset attempts
                unset($_SESSION['login_attempts'][$key]);
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * Clear login attempts for user
     */
    public static function clearLoginAttempts($username) {
        self::startSession();

        if (isset($_SESSION['login_attempts'])) {
            $key = $username . '_' . self::getUserIP();
            unset($_SESSION['login_attempts'][$key]);
        }
    }

    /**
     * Get remaining lockout time
     */
    public static function getLockoutTimeRemaining($username) {
        self::startSession();

        if (!isset($_SESSION['login_attempts'])) {
            return 0;
        }

        $key = $username . '_' . self::getUserIP();

        if (!isset($_SESSION['login_attempts'][$key])) {
            return 0;
        }

        $attempts = $_SESSION['login_attempts'][$key];

        if ($attempts['locked_until'] > 0 && time() < $attempts['locked_until']) {
            return $attempts['locked_until'] - time();
        }

        return 0;
    }

    /**
     * Log security events
     */
    public static function logSecurityEvent($event, $username = null) {
        try {
            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'event' => $event,
                'username' => $username,
                'ip' => self::getUserIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ];

            // Log to file (you can also log to database)
            $logFile = __DIR__ . '/../logs/security.log';
            $logDir = dirname($logFile);

            if (!is_dir($logDir)) {
                if (!mkdir($logDir, 0755, true) && !is_dir($logDir)) {
                    // If we can't create the directory, just return silently
                    return;
                }
            }

            // Check if we can write to the log file
            if (is_writable($logDir) || is_writable($logFile)) {
                file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
            }
        } catch (Exception $e) {
            // Silently fail if logging doesn't work - don't break the application
            error_log("Security logging failed: " . $e->getMessage());
        }
    }
}

/**
 * Utility Functions with Enhanced Security
 */
class Utils {
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Display text safely by decoding HTML entities for frontend display
     * Use this instead of htmlspecialchars() when displaying database content
     */
    public static function displayText($text) {
        if (empty($text)) {
            return '';
        }
        // Decode HTML entities that were encoded during input sanitization
        return html_entity_decode($text, ENT_QUOTES, 'UTF-8');
    }

    public static function generateCSRFToken() {
        Auth::startSession();

        // Generate new token if none exists or if expired
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) ||
            (time() - $_SESSION['csrf_token_time']) > DatabaseConfig::CSRF_TOKEN_LIFETIME) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }

        return $_SESSION['csrf_token'];
    }

    public static function validateCSRFToken($token) {
        Auth::startSession();

        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }

        // Check if token has expired
        if ((time() - $_SESSION['csrf_token_time']) > DatabaseConfig::CSRF_TOKEN_LIFETIME) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }

        return hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Validate file upload security
     */
    public static function validateFileUpload($file, $allowedTypes = null) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['valid' => false, 'error' => 'Invalid file upload'];
        }

        // Check file size
        if ($file['size'] > DatabaseConfig::MAX_FILE_SIZE) {
            return ['valid' => false, 'error' => 'File too large'];
        }

        // Get file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        // Use provided allowed types or default
        $allowed = $allowedTypes ?? DatabaseConfig::ALLOWED_EXTENSIONS;

        if (!in_array($extension, $allowed)) {
            return ['valid' => false, 'error' => 'File type not allowed'];
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp'
        ];

        if (isset($allowedMimes[$extension]) && $mimeType !== $allowedMimes[$extension]) {
            return ['valid' => false, 'error' => 'File type mismatch'];
        }

        return ['valid' => true];
    }

    /**
     * Generate secure random string
     */
    public static function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }

    /**
     * Rate limiting check
     */
    public static function checkRateLimit($action, $limit = 10, $window = 300) {
        Auth::startSession();

        $key = $action . '_' . Auth::getUserIP();

        if (!isset($_SESSION['rate_limits'])) {
            $_SESSION['rate_limits'] = [];
        }

        if (!isset($_SESSION['rate_limits'][$key])) {
            $_SESSION['rate_limits'][$key] = [
                'count' => 0,
                'window_start' => time()
            ];
        }

        $rateLimit = &$_SESSION['rate_limits'][$key];

        // Reset window if expired
        if (time() - $rateLimit['window_start'] > $window) {
            $rateLimit['count'] = 0;
            $rateLimit['window_start'] = time();
        }

        // Check if limit exceeded
        if ($rateLimit['count'] >= $limit) {
            return false;
        }

        // Increment counter
        $rateLimit['count']++;

        return true;
    }

    /**
     * Validate and sanitize URL
     */
    public static function validateURL($url) {
        $url = filter_var($url, FILTER_SANITIZE_URL);
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Secure redirect
     */
    public static function secureRedirect($url, $allowedDomains = []) {
        // Parse URL
        $parsedUrl = parse_url($url);

        // If no host, it's a relative URL - allow it
        if (!isset($parsedUrl['host'])) {
            header('Location: ' . $url);
            exit;
        }

        // Check if domain is allowed
        $currentDomain = $_SERVER['HTTP_HOST'];
        $allowedDomains[] = $currentDomain;

        if (in_array($parsedUrl['host'], $allowedDomains)) {
            header('Location: ' . $url);
            exit;
        }

        // Redirect to safe default
        header('Location: index.php');
        exit;
    }
    
    public static function uploadImage($file, $subfolder = '') {
        $uploadDir = DatabaseConfig::UPLOAD_PATH . $subfolder;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        // Validate file
        if ($file['size'] > DatabaseConfig::MAX_FILE_SIZE) {
            throw new Exception('File size too large');
        }
        
        if (!in_array($fileExtension, DatabaseConfig::ALLOWED_EXTENSIONS)) {
            throw new Exception('Invalid file type');
        }
        
        // Generate unique filename
        $fileName = uniqid() . '.' . $fileExtension;
        $filePath = $uploadDir . '/' . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return $filePath;
        } else {
            throw new Exception('Failed to upload file');
        }
    }
    
    public static function formatDate($date) {
        // Ensure we're using the correct timezone
        $originalTimezone = date_default_timezone_get();
        date_default_timezone_set(DatabaseConfig::TIMEZONE);

        $formatted = date('M d, Y H:i', strtotime($date));

        // Restore original timezone (though it should already be correct)
        date_default_timezone_set($originalTimezone);

        return $formatted;
    }
    
    public static function truncateText($text, $length = 100) {
        return strlen($text) > $length ? substr($text, 0, $length) . '...' : $text;
    }
}