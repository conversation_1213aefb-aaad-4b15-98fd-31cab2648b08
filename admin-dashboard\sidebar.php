<!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full w-64 bg-white shadow-lg sidebar-transition z-50 flex flex-col">
        <!-- Logo -->
        <div class="p-2 border-b border-gray-200 flex justify-center">
            <div class="w-20 h-20 flex items-center justify-center">
                <img src="images/meleva-lg.png" alt="logo" class="max-w-full max-h-full object-contain">
            </div>
        </div>
        
        <!-- Navigation -->
        <nav class="mt-6 flex-grow overflow-y-auto">
            <ul class="space-y-1 px-4">
                <li>
                    <a href="index.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "index.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="users.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "users.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-users w-5"></i>
                        <span>Users</span>
                    </a>
                </li>
                <li>
                    <a href="destinations.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "destinations.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-map-marker-alt w-5"></i>
                        <span>Destinations</span>
                    </a>
                </li>
                <li>
                    <a href="package-types.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "package-types.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-tags w-5"></i>
                        <span>Package Types</span>
                    </a>
                </li>
                <li>
                    <a href="packages.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "packages.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-suitcase w-5"></i>
                        <span>Tour Packages</span>
                    </a>
                </li>
                <li>
                    <a href="quotes.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "quotes.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-file-invoice w-5"></i>
                        <span>Quotes</span>
                    </a>
                </li>

                <li>
                    <a href="payments.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "payments.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-credit-card w-5"></i>
                        <span>Quote Payments</span>
                    </a>
                </li>
                <li>
                    <a href="images.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "images.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-photo-video w-5"></i>
                        <span>Images</span>
                    </a>
                </li>
                <li>
                    <a href="messages.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors relative <?php echo (basename($_SERVER["PHP_SELF"]) == "messages.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-envelope w-5"></i>
                        <span>Messages</span>
                        <?php if ($stats["unread_messages"] > 0): ?>
                            <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-auto"><?php echo $stats["unread_messages"]; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="contact-info.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "contact-info.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-address-book w-5"></i>
                        <span>Contact Info</span>
                    </a>
                </li>
                <li>
                    <a href="manage-reviews.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "manage-reviews.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-star w-5"></i>
                        <span>Manage Reviews</span>
                    </a>
                </li>
                <li>
                    <a href="reports.php" class="flex items-center space-x-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-orange-50 hover:text-orange-600 transition-colors <?php echo (basename($_SERVER["PHP_SELF"]) == "reports.php") ? "text-orange-600 bg-orange-50 sidebar-active" : ""; ?>">
                        <i class="fas fa-chart-line w-5"></i>
                        <span>Reports</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- User Info -->
        <div class="bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800"><?php echo htmlspecialchars($currentUser["username"]); ?></p>
                    <p class="text-xs text-gray-500"><?php echo ucfirst($currentUser["role"]); ?></p>
                </div>
                <a href="logout.php" class="text-gray-400 hover:text-red-500 transition-colors flex items-center space-x-1" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="text-xs">Logout</span>
                </a>
            </div>
        </div>
    </div>

